import { render, screen } from '@redwoodjs/testing/web'
import { MockedProvider } from '@apollo/client/testing'

import AddProduct from './AddProduct'

// Mock the auth hook
jest.mock('src/auth', () => ({
  useAuth: () => ({
    userMetadata: {
      user: { metadata: {} },
      orgIdToOrgMemberInfo: {
        'test-org': {
          orgName: 'Test Org',
          orgMetadata: {}
        }
      }
    }
  }),
  getOrgMemberInfo: (userMetadata) => ({
    orgName: 'Test Org',
    orgMetadata: {}
  })
}))

// Mock other dependencies
jest.mock('src/utils/mapbox', () => ({
  fetchPlacesAutoComplete: jest.fn(),
  getMapboxAccessTokenFromCache: jest.fn(() => null),
  setMapboxAccessTokenToCache: jest.fn()
}))

jest.mock('country-code-lookup', () => ({
  byCountry: jest.fn(() => ({ iso2: 'US' }))
}))

describe('AddProduct', () => {
  const renderWithMocks = (props = {}) => {
    return render(
      <MockedProvider mocks={[]} addTypename={false}>
        <AddProduct {...props} />
      </MockedProvider>
    )
  }

  it('renders successfully', () => {
    expect(() => {
      renderWithMocks()
    }).not.toThrow()
  })

  it('renders scrap rate and scrap fate fields in manufacturing section', () => {
    renderWithMocks()

    // Check if scrap rate field exists
    const scrapRateLabel = screen.getByText('Scrap Rate (%)')
    expect(scrapRateLabel).toBeInTheDocument()

    // Check if scrap fate field exists
    const scrapFateLabel = screen.getByText('Scrap Fate')
    expect(scrapFateLabel).toBeInTheDocument()
  })
})
