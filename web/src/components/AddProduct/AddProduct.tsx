import { useMutation, useQuery } from '@redwoodjs/web'
import { routes, navigate } from '@redwoodjs/router'
import { from, useLazyQuery } from '@apollo/client'
import {
  Layout,
  Breadcrumb,
  Row,
  Col,
  Divider,
  Descriptions,
  Steps,
  Button,
  Form,
  Input,
  AutoComplete,
  InputNumber,
  Space,
  Select,
  Modal,
  Radio,
  Checkbox,
  message,
  Spin,
  notification,
  Collapse,
  CollapseProps,
  Drawer,
  Tag,
  Tooltip,
  Progress,
  Switch,
  Tabs,
  Table,
} from 'antd'
const { Content } = Layout
import {
  TagsOutlined,
  EditOutlined,
  PlusCircleOutlined,
  NumberOutlined,
  PercentageOutlined,
  DeleteOutlined,
  PictureOutlined,
  CloseCircleFilled,
  QuestionCircleOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileOutlined,
  FileTextOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
import DataTable from '../DataTable/DataTable'
const { confirm } = Modal
import mapboxgl from 'mapbox-gl'
import {
  getMapboxAccessTokenFromCache,
  setMapboxAccessTokenToCache,
} from 'src/utils/mapbox'
import axios from 'axios'
import './style.css'
import type { TableColumnsType, TabsProps, UploadFile, UploadProps } from 'antd'
import countries from './countries'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import CopilotLogo from '../CopilotLogo/CopilotLogo'
import PredictionLoadingIcon from '../PredictionLoadingIcon/PredictionLoadingIcon'
import PredictedIcon from '../PredictedIcon/PredictedIcon'
import Paragraph from 'antd/es/typography/Paragraph'
import Dragger from 'antd/es/upload/Dragger'
import FileViewer from '../FileViewer/FileViewer'
import countryCodeLookup from 'country-code-lookup'
import { useUnsavedChangeWarning } from 'src/hooks/useUnsavedChangeWarning'
import { v4 as uuidv4 } from 'uuid'
import { QUERY as PRODUCT_INFO_QUERY } from '../ProductInfoCell'
import { QUERY as FETCH_COMPONENTS_QUERY } from '../ComponentCell'
import { findLastNode, getAncestorNodeOfType, getDescendantNodeOfType, transformNode } from 'src/utils/graph'
import {
  CREATE_EMISSIONS_FACTOR_MUTATION,
  CREATE_SUPPLIER_MUTATION,
  CREATE_PRODUCT_MUTATION,
  CREATE_PRODUCT_PROCESS_MODEL_MUTATION,
  BACKUP_PRODUCT_MUTATION,
  RESTORE_PRODUCT_MUTATION,
  DELETE_PRODUCT_MUTATION,
  ACTIVATE_PRODUCT_MUTATION,
  PREDICT_EMISSIONS_FACTORS_QUERY,
  PREDICT_INGREDIENT_SOURCE_QUERY,
} from 'src/utils/graphql'
import EmissionsFactorSelector from '../EmissionsFactorSelector/EmissionsFactorSelector'
import { formatFloat } from 'src/utils/helper'

export const fetchPlacesAutoComplete = async (
  value,
  country = '',
  types = 'locality,place,district,region,country'
) => {
  try {
    const response = await axios.get(
      `https://api.mapbox.com/search/geocode/v6/forward`,
      {
        params: {
          q: value,
          access_token: mapboxgl.accessToken,
          limit: 5,
          types: types,
          country: country,
        },
      }
    )

    const placesData = response.data.features
      .map((feature) => {
        let placeInfo = {
          district: '',
          region: '',
          country: '',
          country_code: '',
          latitude: null,
          longitude: null,
        }

        if (feature.properties.context) {
          if (
            feature.geometry.coordinates &&
            feature.geometry.coordinates.length === 2
          ) {
            placeInfo.latitude = feature.geometry.coordinates[1]
            placeInfo.longitude = feature.geometry.coordinates[0]
          }

          if (feature.properties.context.district) {
            placeInfo.district = feature.properties.context.district.name
          }

          if (feature.properties.context.region) {
            placeInfo.region = feature.properties.context.region.name
          }

          if (feature.properties.context.country) {
            placeInfo.country = feature.properties.context.country.name
            placeInfo.country_code =
              feature.properties.context.country.country_code_alpha_3
          }

          const countryInfo = countryCodeLookup.byIso(placeInfo.country_code)

          if (!placeInfo.district) {
            placeInfo.district = countryInfo.capital
          }

          if (placeInfo.region) {
            placeInfo.district = placeInfo.region
          }

          return {
            value: feature.properties.full_address,
            label: feature.properties.full_address,
            data: JSON.stringify(placeInfo),
          }
        }

        return null
      })
      .filter((placeInfo) => placeInfo !== null)

    return placesData
  } catch (error) {
    message.error('Failed to fetch location. Error: ' + error.message)
    return null
  }
}

const MAPBOX_ACCESS_TOKEN_QUERY = gql`
  query MapboxAccessTokenQuery {
    getMapboxAccessToken {
      accessToken
      expiresAt
    }
  }
`

const LOG_CREATE_PRODUCT = gql`
  mutation LogCreateProduct {
    logCreateProduct
  }
`

const CREATE_PRODUCT_INGREDIENTS_MUTATION = gql`
  mutation CreateProductIngredients(
    $productId: String!
    $productIngredients: [CreateProductIngredientsInput!]!
  ) {
    createProductIngredients(
      productId: $productId
      productIngredients: $productIngredients
    ) {
      ingredientName
      weight
    }
  }
`

const CREATE_PRODUCT_PACKAGING_MUTATION = gql`
  mutation CreateProductPackaging(
    $productId: String!
    $productPackaging: CreateProductPackagingInput!
  ) {
    createProductPackaging(
      productId: $productId
      productPackaging: $productPackaging
    ) {
      packagingMaterial
      weight
    }
  }
`

const CREATE_PRODUCT_MANUFACTURING_MUTATION = gql`
  mutation CreateProductManufacturing(
    $productId: String!
    $manufacturingProcess: [CreateProductManufacturingProcessInput!]!
  ) {
    createProductManufacturing(
      productId: $productId
      manufacturingProcess: $manufacturingProcess
    )
  }
`

const CREATE_PRODUCT_TRANSPORT_MUTATION = gql`
  mutation CreateProductTransport(
    $productId: String!
    $transportSegment: CreateProductTransportSegmentInput!
  ) {
    createProductTransport(
      productId: $productId
      transportSegment: $transportSegment
    )
  }
`

const PRODUCT_CATEGORIES_QUERY = gql`
  query ProductCategoriesQuery {
    getProductCategories {
      id
      name
    }
  }
`

const RAW_MATERIALS_QUERY = gql`
  query RawMaterialsQuery($isPackaging: Boolean) {
    getRawMaterials(isPackaging: $isPackaging) {
      name
      description
      casNumber
    }
  }
`

const PREDICT_PRODUCT_CATEGORY_QUERY = gql`
  query PredictProductCategoryQuery($productName: String!) {
    predictProductCategory(productName: $productName) {
      id
      name
    }
  }
`

const PREDICT_MANUFACTURING_PROCESSES_QUERY = gql`
  query PredictManufacturingProcesses(
    $productName: String!
    $productCategory: String!
  ) {
    predictManufacturingProcesses(
      productName: $productName
      productCategory: $productCategory
    )
  }
`

const PREDICT_CONSUMER_USE_QUERY = gql`
  query PredictConsumerUse($productCategory: String!, $geographyIso3: String!) {
    predictConsumerUse(
      productCategory: $productCategory
      geographyIso3: $geographyIso3
    ) {
      name
      amount
      unit
    }
  }
`

const PREDICT_PRODUCT_PACKAGING_QUERY = gql`
  query PredictProductPackagingQuery(
    $productCategory: String!
    $weight: Float!
  ) {
    predictProductPackaging(
      productCategory: $productCategory
      weight: $weight
    ) {
      component
      material
      weight
      numberOfPackages
      packagingLevel
    }
  }
`

const EXTRACT_FILE_QUERY = gql`
  query ExtractFileQuery($base64Data: String!, $contentType: String!) {
    extractFile(base64Data: $base64Data, contentType: $contentType) {
      productName
      productID
      annualSalesVolume
      factoryCity
      factoryCountry
      nodes {
        id
        name
        component
        description
        packagingLevel
        nodeType
        location {
          city
          country
        }
        amount
        quantity
        unit
        scrapRate
        scrapFate
      }
      edges {
        fromNodeId
        toNodeId
      }
      components {
        componentId
        quantity
      }
    }
  }
`

const EXTRACT_COMPONENTS_FROM_FILE_QUERY = gql`
  query ExtractComponentsFromFileQuery(
    $base64Data: String!
    $contentType: String!
  ) {
    extractComponentsFromFile(
      base64Data: $base64Data
      contentType: $contentType
    ) {
      components {
        componentId
        componentName
        nodes {
          id
          name
          component
          description
          nodeType
          location {
            city
            country
          }
          amount
          quantity
          unit
          scrapRate
          scrapFate
        }
        edges {
          fromNodeId
          toNodeId
        }
      }
      warnings
    }
  }
`

const UPDATE_INGREDIENT_EMISSIONS_FACTORS_MUTATION = gql`
  mutation UpdateIngredientEmissionsFactors(
    $emissionsFactors: [EmissionsFactorInput!]!
  ) {
    updateIngredientEmissionsFactors(emissionsFactors: $emissionsFactors)
  }
`

const MANUFACTURING_METHODS_QUERY = gql`
  query ManufacturingMethodsQuery {
    getManufacturingMethods {
      processName
      geographyIso3
      electricityKwh
      waterLiters
      co2eKg
      amountOfProductKg
    }
  }
`

const AddProduct = ({
  importFile = false,
  editMode = false,
  productId = null,
  initialStep = null,
  component = false,
}) => {
  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const orgName =
    orgMemberInfo && orgMemberInfo.orgName ? orgMemberInfo.orgName : null

  const [getProductDetails, { loading: getProductDetailsIsLoading }] =
    useLazyQuery(PRODUCT_INFO_QUERY)
  const [getComponents, { loading: getComponentsIsLoading }] =
    useLazyQuery(FETCH_COMPONENTS_QUERY, {
      variables: { isComponent: true },
    })

  const [editProductIsLoading, setEditProductIsLoading] = useState(false)
  const [_productDetails, setProductDetails] = useState(null)
  const [componentDetails, setComponentDetails] = useState(null)
  const [isImportComponent, setIsImportComponent] = useState(component)
  const [basicProductEditing, setBasicProductEditing] = useState((orgMemberInfo?.orgMetadata?.basicProductEditing ?? false) && editMode)

  const [processModelData, setProcessModelData] = useState(null)

  const fetchMapboxAutoCompleteResponse = async (country, city = null) => {
    let countryInfo = null

    try {
      countryInfo = countryCodeLookup.byCountry(country)
    } catch (error) {
      console.error('Error looking up country code: ' + error)
      return false
    }

    if (!countryInfo) {
      console.error('Failed to find country code for: ' + country)
      return false
    }

    let mapboxAutoCompleteResponse = null

    if (city) {
      mapboxAutoCompleteResponse = await fetchPlacesAutoComplete(
        city,
        countryInfo.iso2
      )
    }

    if (!mapboxAutoCompleteResponse?.length) {
      // If no results are found for the city, try searching for the country
      mapboxAutoCompleteResponse = await fetchPlacesAutoComplete(
        country,
        countryInfo.iso2
      )
    }

    return mapboxAutoCompleteResponse
  }

  const _buildSupplierLocation = (city, country) => {
    if (!country) {
      return null
    }

    return {
      value: city ? `${city}, ${country}` : country,
      label: city ? `${city}, ${country}` : country,
      data: JSON.stringify({
        district: city,
        region: '',
        country,
        country_code: '',
        latitude: null,
        longitude: null,
      })
    }
  }

  const renderProductInfo = async (productDetails) => {
    const stepMap = {
      productInfo: 0,
      rawMaterials: 1,
      packaging: 1,
      manufacturing: 2,
      transportation: 3,
      consumerUse: 4,
      endOfLife: 5,
    }

    const factoryLocationData = _buildSupplierLocation(
      productDetails.factoryCity,
      productDetails.factoryCountry
    )

    if (!factoryLocationData) {
      message.error('Failed to fetch factory location data')
      return
    }
    const countryOfUseLocationData = _buildSupplierLocation(
      null,
      productDetails.countryOfUse
    )

    if (!countryOfUseLocationData) {
      message.error('Failed to fetch country of use location data')
      return
    }

    setAddProductOptioinalDataCollapseKey(['1'])

    const _productInfo = {
      productName: productDetails.productName,
      category: productDetails.category,
      brand: productDetails.brand,
      sku: productDetails.productId,
      annualSalesVolume: productDetails.annualSalesVolumeUnits,
      tags: productDetails.tags,
      functionalUnit: productDetails.functionalUnit,
      factoryLocation: factoryLocationData.label,
      dataField_factoryLocation: factoryLocationData?.data,
      targetMarketLocation: productDetails.countryOfUse,
      dataField_targetMarketLocation: countryOfUseLocationData.data,
      imageUrl: productDetails.productImage,
    }
    addProductForm.setFieldsValue(_productInfo)

    await addProductForm.validateFields()
    setProductInfo(_productInfo)

    fetchManufacturingMethods()

    const ingredientsData = []
    const rawMaterialNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'material'
    )
    for (const ingredient of rawMaterialNodes) {
      let supplierLocationData = null
      if (ingredient.location.country) {
        supplierLocationData = _buildSupplierLocation(
          ingredient.location.city,
          ingredient.location.country
        )
      }

      let ingredientData = {
        key: uuidv4(),
        ingredient: ingredient.name,
        component: ingredient.component,
        description: ingredient.description,
        weight: ingredient.amount,
        weightUnit: ingredient.unit,
        casNo: null,
        supplierName: ingredient.supplier?.supplierName,
        supplierOrigin: supplierLocationData
          ? supplierLocationData?.label
          : null,
        dataField_supplierLocation: supplierLocationData
          ? supplierLocationData?.data
          : null,
        recycledContent: ingredient.recycledContent,
        emissionsFactorMatch: ingredient.emissionsFactor ?? null,
        emissionsFactorMatches: [],
        emissionsFactorMatchActivityName:
          ingredient.emissionsFactor.activityName ?? null,
        emissionsFactorMatchSource: ingredient.emissionsFactor.source ?? null,
        predictEmissionsFactorMatchIsLoading: false,
        predictSourceIsLoading: false,
        location: {
          city: ingredient.location.city,
          country: ingredient.location.country,
        },
      }

      ingredientsData.push(ingredientData)
    }
    setIngredientTableDataSource(ingredientsData)
    setAddIngredientTableKey(uuidv4())

    //Packaging
    const packagingData = []
    const packagingNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'packaging'
    )
    for (const packagingNode of packagingNodes) {
      let supplierLocationData = null
      if (packagingNode.location?.country) {
        supplierLocationData = _buildSupplierLocation(
          packagingNode.location.city,
          packagingNode.location.country
        )
      }
      const packagingMaterial = {
        key: uuidv4(),
        material: packagingNode.name,
        component: packagingNode.component,
        description: packagingNode.description,
        weight: packagingNode.amount,
        weightUnit: packagingNode.unit,
        tier: packagingNode.packagingLevel,
        supplierName: packagingNode.supplier?.supplierName,
        supplierOrigin: supplierLocationData
          ? supplierLocationData?.label
          : null,
        dataField_supplierLocation: supplierLocationData
          ? supplierLocationData?.data
          : null,
        recycledContent: packagingNode.recycledContent,
        emissionsFactorMatch: packagingNode.emissionsFactor ?? null,
        emissionsFactorMatches: [],
        emissionsFactorMatchActivityName:
          packagingNode.emissionsFactor.activityName ?? null,
        emissionsFactorMatchSource:
          packagingNode.emissionsFactor.source ?? null,
        predictEmissionsFactorMatchIsLoading: false,
        predictSourceIsLoading: false,
        location: {
          city: packagingNode.location?.city,
          country: packagingNode.location?.country,
        },
      }
      packagingData.push(packagingMaterial)
    }
    setPackagingTableDataSource(packagingData)
    setAddPackagingTableKey(uuidv4())

    //Manufacturing
    const manufacturingData = []
    const manufacturingNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'production'
    )

    for (const manufacturingNode of manufacturingNodes) {
      const manufacturingProcess = {
        key: uuidv4(),
        activityName: manufacturingNode.name,
        activityType: manufacturingNode.component ?? 'Total Carbon Emissions',
        amount: null,
        unit: manufacturingNode.unit,
        amountOfProductKg: 1000,
        predictEmissionsFactorMatchIsLoading: false,
        emissionsFactorMatch: manufacturingNode.emissionsFactor ?? null,
        emissionsFactorMatchActivityName:
          manufacturingNode.emissionsFactor?.activityName ?? null,
        location: {
          city: manufacturingNode.location.city,
          country: manufacturingNode.location.country,
        },
      }
      manufacturingData.push(manufacturingProcess)
    }

    setManufacturingMethodTableDataSource(manufacturingData)
    setAddManufacturingProcessTableKey(uuidv4())

    const transportData = []

    // Default transport segment
    transportData.push({
      key: uuidv4(),
      segmentName: 'Source to Factory',
      destination: addProductForm.getFieldValue('factoryLocation'),
      dataField_destination: addProductForm.getFieldValue(
        'dataField_factoryLocation'
      ),
    })

    const factoryToretailSegment = productDetails.nodes.find(
      (x) => x.nodeType === 'transportation' && x.name === 'Factory to Retail'
    )

    const retailToCustomerSegment = productDetails.nodes.filter(
      (x) => x.nodeType === 'transportation' && x.name === 'Retail to Customer'
    )

    const countryOfUseLocation = JSON.parse(_productInfo.dataField_targetMarketLocation)

    transportData.push({
      key: uuidv4(),
      segmentName: 'Factory to Retail',
      destination: `${factoryToretailSegment?.location?.city ?? countryOfUseLocation.district}, ${factoryToretailSegment?.location?.country ?? countryOfUseLocation.country}`,
      dataField_destination: JSON.stringify({
        district: factoryToretailSegment?.location?.city ?? countryOfUseLocation.district,
        region: '',
        country: factoryToretailSegment?.location?.country ?? countryOfUseLocation.country,
        country_code: '',
        latitude: null,
        longitude: null,
      }),
    })

    for (const segment of retailToCustomerSegment) {
      transportData.push({
        key: uuidv4(),
        segmentName: 'Retail to Customer',
        destination: `${segment?.location?.city}, ${segment?.location?.country}`,
        dataField_destination: JSON.stringify({
          district: segment?.location?.city,
          region: '',
          country: segment?.location?.country,
          country_code: '',
          latitude: null,
          longitude: null,
        }),
      })
    }

    setTransportationTableDataSource(transportData)
    setAddTransportationTableKey(uuidv4())

    //consumerUse
    const consumerUseData = []
    const consumerUseNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'use'
    )
    for (const consumerUseNode of consumerUseNodes) {
      const consumerUse = {
        key: uuidv4(),
        resource: consumerUseNode.name,
        amount: consumerUseNode.amount,
        unit: consumerUseNode.unit,
        predictEmissionsFactorMatchIsLoading: false,
        emissionsFactorMatch: consumerUseNode.emissionsFactor ?? null,
        emissionsFactorMatchActivityName:
          consumerUseNode.emissionsFactor?.activityName ?? null,
      }
      consumerUseData.push(consumerUse)
    }
    setConsumerUseTableDataSource(consumerUseData)
    setConsumerUseTableKey(uuidv4())

    setCurrentStep(stepMap[initialStep] || 0)

    setEditProductIsLoading(false)
  }

  const [mapBoxAccessToken, setMapBoxAccessToken] = useState(
    getMapboxAccessTokenFromCache()
  )

  const [
    fetchMapboxAccessToken,
    {
      data: mapboxAccessTokenData,
      error: mapboxAccessTokenDataError,
      loading: mapboxAccessTokenLoading,
    },
  ] = useLazyQuery(MAPBOX_ACCESS_TOKEN_QUERY)

  useEffect(() => {
    if (mapBoxAccessToken) {
      mapboxgl.accessToken = mapBoxAccessToken.accessToken
      setMapboxAccessTokenToCache(mapBoxAccessToken)
    }
    if (!mapBoxAccessToken) {
      fetchMapboxAccessToken()
    }
  }, [mapBoxAccessToken, fetchMapboxAccessToken])

  useEffect(() => {
    if (mapboxAccessTokenData) {
      setMapBoxAccessToken(mapboxAccessTokenData.getMapboxAccessToken)
    }
  }, [mapboxAccessTokenData])

  useEffect(() => {
    if (userMetadata?.user?.metadata?.calculateEmissionsPerUnit) {
      setCalculateEmissionsPerUnit(true)
    }
  }, [userMetadata])

  useEffect(() => {
    if (editMode && productId && mapBoxAccessToken) {
      setEditProductIsLoading(true)
      getProductDetails({
        variables: { productId, calculateEmissionsPerUnit: true },
      }).then((response) => {
        if (response.data && response.data.getProductInfo) {
          const productDetails = response.data.getProductInfo
          setProductDetails(productDetails)
          renderProductInfo(productDetails)
        } else {
          message.error('Failed to fetch product details')
        }
      })
    }
  }, [editMode, productId, mapBoxAccessToken])

  const [isFileImportComplete, setIsFileImportComplete] = useState(!importFile)
  const [uploadedFile, setUploadedFile] = useState({
    base64Data: null,
    contentType: null,
    name: null,
  })
  const [showFilePreview, setShowFilePreview] = useState(false)
  const [extractFileIsLoading, setExtractFileIsLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [currentAddExchangeStep, setCurrentAddExchangeStep] = useState(0)
  const [productInfo, setProductInfo] = useState(null)
  const [componentTableDataSource, setComponentTableDataSource] = useState([])
  const [ingredientTableDataSource, setIngredientTableDataSource] = useState([])
  const [calculateEmissionsPerUnit, setCalculateEmissionsPerUnit] = useState(false)
  const [packagingTableDataSource, setPackagingTableDataSource] = useState([])
  const [
    intermediateExchangesTableDataSource,
    setIntermediateExchangesTableDataSource,
  ] = useState([])
  const [
    emissionsFactorsActivityMatchesTableDataSource,
    setEmissionsFactorsActivityMatchesTableDataSource,
  ] = useState([])
  const [
    manufacturingMethodTableDataSource,
    setManufacturingMethodTableDataSource,
  ] = useState([])
  const [customManufacturingEF, setCustomManufacturingEF] = useState(false)
  const [transportationTableDataSource, setTransportationTableDataSource] =
    useState([])
  const [consumerUseTableDataSource, setConsumerUseTableDataSource] = useState(
    []
  )
  const [addIngredientTableKey, setAddIngredientTableKey] = useState(uuidv4())
  const [addComponentTableKey, setAddComponentTableKey] = useState(uuidv4())
  const [addManufacturingProcessTableKey, setAddManufacturingProcessTableKey] =
    useState(uuidv4())
  const [addTransportationTableKey, setAddTransportationTableKey] = useState(
    uuidv4()
  )
  const [consumerUseTableKey, setConsumerUseTableKey] = useState(uuidv4())
  const [
    emissionsFactorsActivityMatchesTableKey,
    setEmissionsFactorsActivityMatchesTableKey,
  ] = useState(uuidv4())
  const [intermediateExchangesTableKey, setIntermediateExchangesTableKey] =
    useState(uuidv4())
  const [addPackagingTableKey, setAddPackagingTableKey] = useState(uuidv4())
  const [
    emissionsFactorsSearchResultsTableKey,
    setEmissionsFactorsSearchResultsTableKey,
  ] = useState(uuidv4())
  const [addProductForm] = Form.useForm()
  const [addComponentForm] = Form.useForm()
  const [addIngredientForm] = Form.useForm()
  const [addPackagingForm] = Form.useForm()
  const [addIntermediateExchangeForm] = Form.useForm()
  const [emissionsFactorMatchForm] = Form.useForm()
  const [addManufacturingMethodForm] = Form.useForm()
  const [addTransportationForm] = Form.useForm()
  const [
    addProductOptioinalDataCollapseKey,
    setAddProductOptioinalDataCollapseKey,
  ] = useState(['0'])
  const [isAddIngredientModalOpen, setIsAddIngredientModalOpen] =
    useState(false)
  const [isAddPackagingModalOpen, setIsAddPackagingModalOpen] = useState(false)
  const [
    isAddManufacturingMethodModalOpen,
    setIsAddManufacturingMethodModalOpen,
  ] = useState(false)
  const [selectedManufacturingActivity, setSelectedManufacturingActivity] =
    useState('Total Carbon Emissions')
  const [isAddTransportationModalOpen, setIsAddTransportationModalOpen] =
    useState(false)

  const [emissionsFactorSearchValue, setEmissionsFactorSearchValue] =
    useState(null)

  const [
    isAddIntermediateExchangeModalOpen,
    setIsAddIntermediateExchangeModalOpen,
  ] = useState(false)

  const [
    getManufacturingMethods,
    { loading: getManufacturingMethodsIsLoading },
  ] = useLazyQuery(MANUFACTURING_METHODS_QUERY)

  const [
    advancedAddIngredientFieldsIsChecked,
    setAdvancedAddIngredientFieldsIsChecked,
  ] = useState(false)
  const [
    advancedAddPackagingFieldsIsChecked,
    setAdvancedAddPackagingFieldsIsChecked,
  ] = useState(false)

  const [
    predictIngredientEmissionsFactorsLoading,
    setPredictIngredientEmissionsFactorsLoading,
  ] = useState(false)

  const [formIsLoading, setFormIsLoading] = useState(false)

  const [
    productInfoFactoryLocationMapboxDataLoading,
    setProductInfoFactoryLocationMapboxDataLoading,
  ] = useState(false)
  const [
    ingredientSupplierLocationMapboxDataLoading,
    setIngredientSupplierLocationMapboxDataLoading,
  ] = useState(false)
  const [
    packagingSupplierLocationMapboxDataLoading,
    setPackagingSupplierLocationMapboxDataLoading,
  ] = useState(false)
  const [
    transportSegmentDestinationMapboxDataLoading,
    setTransportSegmentDestinationMapboxDataLoading,
  ] = useState(false)

  const [
    emissionsFactorMatchesDrawerIsOpen,
    setEmissionsFactorActivityMatchesDrawerIsOpen,
  ] = useState(false)
  const [
    emissionsFactorIntermediateExchangesDrawerIsOpen,
    setEmissionsFactorIntermediateExchangesDrawerIsOpen,
  ] = useState(false)

  const [selectedChemicalName, setSelectedChemicalName] = useState({
    chemicalName: null,
    casNo: null,
    activityType: null,
    unit: null,
    geography: null,
    currentEmissionsFactor: null,
    emissionsFactorMatches: null,
  })
  const [manufacturingMethods, setManufacturingMethods] = useState([])
  const [emissionsFactorsSearchResults, setEmissionsFactorsSearchResults] =
    useState([])
  const [selectedActivity, setSelectedActivity] = useState({
    activityName: null,
    referenceProduct: null,
    geography: null,
    source: null,
    unit: null,
  })

  const [selectedExchangeActivity, setSelectedExchangeActivity] = useState({
    activityName: null,
    referenceProduct: null,
    geography: null,
    source: null,
    unit: null,
  })

  const [selectedParentActivity, setSelectedParentActivity] = useState({
    activityName: null,
    referenceProduct: null,
    geography: null,
    source: null,
    unit: null,
  })

  const [createProduct, { loading: createProductIsLoading }] = useMutation(
    CREATE_PRODUCT_MUTATION
  )

  const [createSupplier, { loading: createSupplierIsLoading }] = useMutation(
    CREATE_SUPPLIER_MUTATION
  )

  const [
    createProductProcessModel,
    { loading: createProductProcessModelIsLoading },
  ] = useMutation(CREATE_PRODUCT_PROCESS_MODEL_MUTATION)

  const [deleteProduct, { loading: deleteProductLoading }] = useMutation(
    DELETE_PRODUCT_MUTATION
  )

  const [backupProduct, { loading: backupProductIsLoading }] = useMutation(
    BACKUP_PRODUCT_MUTATION
  )

  const [restoreProduct, { loading: restoreProductIsLoading }] = useMutation(
    RESTORE_PRODUCT_MUTATION
  )

  const [createEmissionsFactor, { loading: createEmissionsFactorIsLoading }] =
    useMutation(CREATE_EMISSIONS_FACTOR_MUTATION)

  const [
    createProductIngredients,
    { loading: createProductIngredientsIsLoading },
  ] = useMutation(CREATE_PRODUCT_INGREDIENTS_MUTATION)

  const [
    updateIngredienEmissionsFactors,
    { loading: updateIngredienEmissionsFactorsIsLoading },
  ] = useMutation(UPDATE_INGREDIENT_EMISSIONS_FACTORS_MUTATION)

  const [createProductPackaging, { loading: createProductPackagingIsLoading }] =
    useMutation(CREATE_PRODUCT_PACKAGING_MUTATION)

  const [
    createManufacturingProcesses,
    { loading: createManufacturingProcessesIsLoading },
  ] = useMutation(CREATE_PRODUCT_MANUFACTURING_MUTATION)

  const [createTransportSegment, { loading: createTransportSegmentIsLoading }] =
    useMutation(CREATE_PRODUCT_TRANSPORT_MUTATION)

  const [activateProduct, { loading: activateProductIsLoading }] = useMutation(
    ACTIVATE_PRODUCT_MUTATION
  )

  const [logCreateProduct] = useMutation(LOG_CREATE_PRODUCT)

  useUnsavedChangeWarning(true)

  const [mapboxAutocompleteOptions, setMapboxAutocompleteOptions] = useState([])
  const [productCategories, setProductCategories] = useState([])
  const [rawMaterialsIngredient, setRawMaterialsIngredient] = useState([])
  const [rawMaterialsPackaging, setRawMaterialsPackaging] = useState([])
  const [manufacturingActivityName, setManufacturingActivityName] = useState([])
  const [TransportSegment, setTransportSegment] = useState([
    {
      option: 'Factory to Retail',
      value: 'Factory to Retail',
    },
    {
      option: 'Retail to Customer',
      value: 'Retail to Customer',
    },
    {
      option: 'Retail to Customer (ecommerce)',
      value: 'Retail to Customer (ecommerce)',
    },
  ])
  const [transportSegmentTooltipMessage, setTransportSegmentTooltipMessage] =
    useState('Specify the name of the transportation step/segment')
  const [predictedFormValues, setPredictedFormValues] = useState({
    category: false,
  })

  const [fileUploadPercent, setFileUploadPercent] = useState(0)
  const [fileUploadStatusMessage, setFileUploadStatusMessage] = useState('Uploading File')

  const { data: productCategoryData, error: productCategoryDataError } =
    useQuery(PRODUCT_CATEGORIES_QUERY)

  const {
    data: rawMaterialsIngredientData,
    error: rawMaterialsIngredientDataError,
  } = useQuery(RAW_MATERIALS_QUERY)

  const {
    data: rawMaterialsPackagingData,
    error: rawMaterialsPackagingDataError,
  } = useQuery(RAW_MATERIALS_QUERY, {
    variables: { isPackaging: true },
  })

  useEffect(() => {
    if (productCategoryData) {
      const categories = productCategoryData.getProductCategories.map(
        (category) => ({
          value: category.name,
          label: category.name,
        })
      )
      return setProductCategories(categories)
    }
    if (productCategoryDataError) {
      message.error('Failed to fetch product categories')
      return console.error(
        'Error fetching product categories:',
        productCategoryDataError
      )
    }
  }, [productCategoryData, productCategoryDataError])

  useEffect(() => {
    if (rawMaterialsIngredientData) {
      const ingredients = rawMaterialsIngredientData.getRawMaterials.map(
        (rawMaterial) => ({
          value: rawMaterial.name,
          label: rawMaterial.name,
          casNumber: rawMaterial.casNumber,
        })
      )
      return setRawMaterialsIngredient(ingredients)
    }
    if (rawMaterialsIngredientDataError) {
      message.error('Failed to fetch ingredients')
      return console.error(
        'Error fetching ingredients:',
        rawMaterialsIngredientDataError
      )
    }
  }, [rawMaterialsIngredientData, rawMaterialsIngredientDataError])

  useEffect(() => {
    if (mapboxAccessTokenData) {
      setMapBoxAccessToken(mapboxAccessTokenData.getMapboxAccessToken)
    }
  }, [mapboxAccessTokenData])

  useEffect(() => {
    if (rawMaterialsPackagingData) {
      const packaging = rawMaterialsPackagingData.getRawMaterials.map(
        (rawMaterial) => ({
          value: rawMaterial.name,
          label: rawMaterial.name,
        })
      )
      return setRawMaterialsPackaging(packaging)
    }
    if (rawMaterialsPackagingDataError) {
      message.error('Failed to fetch packaging materials')
      return console.error(
        'Error fetching packaging materials:',
        rawMaterialsPackagingDataError
      )
    }
  }, [rawMaterialsPackagingData, rawMaterialsPackagingDataError])

  const [
    predictProductCategory,
    {
      data: predictedProductCategoryData,
      error: predictedProductCategoryDataError,
      loading: predictedProductCategoryLoading,
    },
  ] = useLazyQuery(PREDICT_PRODUCT_CATEGORY_QUERY)

  const [
    predictManufacturingProcesses,
    { loading: predictManufacturingProcessesIsLoading },
  ] = useLazyQuery(PREDICT_MANUFACTURING_PROCESSES_QUERY)

  const [predictConsumerUse, { loading: predictConsumerUseIsLoading }] =
    useLazyQuery(PREDICT_CONSUMER_USE_QUERY)

  const [
    predictProductPackaging,
    {
      data: predictedProductPackagingData,
      error: predictedProductPackagingDataError,
      loading: predictedProductPackagingIsLoading,
    },
  ] = useLazyQuery(PREDICT_PRODUCT_PACKAGING_QUERY)

  const [extractFile] = useLazyQuery(EXTRACT_FILE_QUERY)
  const [extractComponentsFromFile] = useLazyQuery(
    EXTRACT_COMPONENTS_FROM_FILE_QUERY
  )

  useEffect(() => {
    if (!extractFileIsLoading) return

    const startTime = Date.now()

    const updateProgress = () => {
      const elapsedTime = Date.now() - startTime
      const elapsedSeconds = elapsedTime / 1000

      if (!extractFileIsLoading) {
        return
      }

      if (elapsedSeconds <= 10) {
        setFileUploadPercent(5)
        setFileUploadStatusMessage('Uploading file')
      } else if (elapsedSeconds <= 20) {
        setFileUploadPercent(30)
        setFileUploadStatusMessage('Reading data')
      } else if (elapsedSeconds <= 30) {
        setFileUploadPercent(60)
        setFileUploadStatusMessage('Processing data')
      } else if (elapsedSeconds <= 40) {
        setFileUploadPercent(80)
        setFileUploadStatusMessage('Validating data')
      } else if (elapsedSeconds <= 240) {
        setFileUploadPercent(90)
        setFileUploadStatusMessage('Predicting Emissions Factors')
      }
    }

    const intervalToCheck = setInterval(updateProgress, 2000)
    return () => clearInterval(intervalToCheck)
  }, [extractFileIsLoading])

  const handleProductNameChange = async () => {
    const productName = addProductForm.getFieldValue('productName')
    if (!productName || !productName.trim().length) {
      return false
    }
    await predictProductCategory({ variables: { productName } })
  }

  const [
    predictEmissionsFactors,
    { loading: predictEmissionsFactorsIsLoading },
  ] = useLazyQuery(PREDICT_EMISSIONS_FACTORS_QUERY)

  const [
    predictIngredientSource,
    { loading: predictIngredientSourceIsLoading },
  ] = useLazyQuery(PREDICT_INGREDIENT_SOURCE_QUERY)

  useEffect(() => {
    if (predictedProductCategoryData) {
      const predictedProductCategory =
        predictedProductCategoryData.predictProductCategory.name
      addProductForm.setFieldsValue({
        category: predictedProductCategory,
      })
      setPredictedFormValues((prevValues) => {
        return { ...prevValues, category: true }
      })
    }
    if (predictedProductCategoryDataError) {
      return console.error(
        'Error predicting product category:',
        predictedProductCategoryDataError
      )
    }
  }, [predictedProductCategoryData, predictedProductCategoryDataError])

  useEffect(() => {
    if (predictedProductPackagingData) {
      if (!predictedProductPackagingData.predictProductPackaging.length) {
        return notification.warning({
          placement: 'topRight',
          message: 'No predicted packaging found',
          description: `Product category ${addProductForm.getFieldValue(
            'category'
          )} has no predicted packaging`,
        })
      }

      const predictedProductPackaging =
        predictedProductPackagingData.predictProductPackaging.map(
          (packaging, index) => {
            const packagingData = {
              key: uuidv4(),
              component: packaging.component,
              material: packaging.material.toUpperCase(),
              weight: formatFloat(packaging.weight ?? 0, 4),
              weightUnit: 'g',
              tier: packaging.packagingLevel,
              recycledContent: packaging.recycledContent,
              predicted: true,
              predictSourceIsLoading: true,
            }

            handlePredictPackagingSource(packagingData)

            return packagingData
          }
        )

      setPackagingTableDataSource(predictedProductPackaging)
      setAddPackagingTableKey(uuidv4())
    }
    if (predictedProductPackagingDataError) {
      return console.error(
        'Error predicting product category:',
        predictedProductPackagingDataError
      )
    }
  }, [predictedProductPackagingData, predictedProductPackagingDataError])

  const handleCreateProduct = async ({
    product,
    oldProductId = null
  }) => {
    const factoryLocation = JSON.parse(product.dataField_factoryLocation)
    const targetCountry = JSON.parse(product.dataField_targetMarketLocation)

    const createProductInput = {
      product_id: product.sku ?? Date.now().toString(),
      product_name: product.productName,
      product_type: 'product',
      brand: product.brand ?? orgName,
      country_of_use: targetCountry.country,
      factory_country: factoryLocation.country,
      factory_city: factoryLocation.district,
      primary_category: product.category ?? 'NA',
      functional_unit: product.functionalUnit,
      product_image: product.imageUrl ?? null,
      annual_sales_volume_units: parseInt(product.annualSalesVolume) ?? null,
      tags: product.tags,
      old_product_id: oldProductId ? `${oldProductId}_backup` : null,
    }

    const response = await createProduct({
      variables: {
        product: createProductInput,
      },
    })

    return response.data
  }

  const handleActivateProduct = async (productId) => {
    const response = await activateProduct({
      variables: {
        productId: productId,
      },
    })

    return response.data
  }

  const handleCreateSupplier = async (material) => {
    const supplierLocation = JSON.parse(material.dataField_supplierLocation)

    const supplier = {
      supplier_name:
        material.supplierName ??
        `${material.ingredient} Supplier for ${productInfo.productName}`,
      supplier_type: 'Raw Material',
      supplier_level: 2,
      city: supplierLocation?.district,
      country: supplierLocation.country,
      latitude: supplierLocation.latitude,
      longitude: supplierLocation.longitude,
    }

    const response = await createSupplier({
      variables: {
        supplier: supplier,
      },
    })

    return response.data
  }

  const handleCreateEmissionsFactor = async (
    emissionsFactor,
    activityType = 'Raw Materials'
  ) => {

    const emissionsFactorInput = {
      parent_emissions_factor: {
        activity_name: emissionsFactor.activityName,
        activity_type: activityType,
        unit: emissionsFactor.unit,
        reference_product_name: emissionsFactor.referenceProduct,
        geography: emissionsFactor.geography,
        source: emissionsFactor.source,
        kg_co2e: emissionsFactor.kgCO2e,
      },
      exchanges: emissionsFactor.exchanges?.map((exchange) => {
        return {
          exchange_name: exchange.exchangeName,
          amount: exchange.amount,
          unit: exchange.unit,
          input_stream: exchange.inputStream,
          exchange_emissions_factor: {
            activity_name: exchange.exchangeEmissionsFactor.activityName,
            activity_type:
              exchange.exchangeEmissionsFactor.activityType ?? activityType,
            reference_product_name:
              exchange.exchangeEmissionsFactor.referenceProduct,
            geography: exchange.exchangeEmissionsFactor.geography,
            source: exchange.exchangeEmissionsFactor.source,
            unit: exchange.exchangeEmissionsFactor.unit,
          },
        }
      }) ?? [],
      elemental_ef_values: emissionsFactor.elementalEfValues?.map((ef) => {
        return {
          lcia_method: ef.lciaMethod,
          impact_category_name: ef.impactCategoryName,
          impact_category_indicator: ef.impactCategoryIndicator,
          impact_category_unit: ef.impactCategoryUnit,
          amount: ef.amount,
        }
      }) ?? []
    }

    try {
      const response = await createEmissionsFactor({
        variables: {
          emissionsFactor: emissionsFactorInput,
        },
      })

      return response.data
    } catch (error) {
      console.error('Error creating emissions factor:', error)
      return {
        emissionsFactor: {
          createEmissionsFactor: emissionsFactor,
        },
      }
    }
  }

  const generateNodeId = (() => {
    let counter = 1;
    return () => counter++;
  })();

  const handleCreateProcessModel = async (productId) => {
    const nodes = []
    const edges = []

    const factoryLocation = JSON.parse(
      addProductForm.getFieldValue('dataField_factoryLocation') ??
        productInfo.dataField_factoryLocation
    )

    const getManufacturingEF = async (manufacturingProcess) => {
      const manufacturingEFMap = {
        'Electricity usage': {
          activityName:
            'electricity production, photovoltaic, 3kWp slanted-roof installation, multi-Si, panel, mounted',
          referenceProduct: 'water, deionised',
          geography: 'RoW',
          source: 'Ecoinvent 3.11',
          unit: 'kg',
          kgCO2e: 0.0803647,
          exchanges: [],
          elementalEfValues: [],
        },
        Water: {
          activityName: 'water production, deionised',
          referenceProduct: 'water, deionised',
          geography: 'RoW',
          source: 'Ecoinvent 3.11',
          unit: 'kg',
          kgCO2e: 0.000495767,
          exchanges: [],
          elementalEfValues: [],
        },
        'Total Carbon Emissions': {
          activityName:
            manufacturingProcess.emissionsFactorMatch?.activityName ??
            manufacturingProcess.activityType,
          referenceProduct:
            manufacturingProcess.emissionsFactorMatch?.referenceProduct ??
            manufacturingProcess.activityName,
          geography:
            manufacturingProcess.emissionsFactorMatch?.geography ?? 'RoW',
          source: manufacturingProcess.emissionsFactorMatch?.source ?? 'Custom',
          kgCO2e: manufacturingProcess.amount ?? null,
          unit: 'kg',
          exchanges: [],
          elementalEfValues: [],
        },
      }

      return manufacturingEFMap[manufacturingProcess.activityType]
    }

    for (const material of ingredientTableDataSource) {
      const supplierLocation = JSON.parse(material.dataField_supplierLocation)

      const supplier = await handleCreateSupplier(material)

      let emissionsFactorMatch = material.emissionsFactorMatch

      try {
        if (emissionsFactorMatch?.modified) {
          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        }
      } catch (error) {
        console.error('Error creating emissions factor:', error)
      } finally {
        const materialNodeId = generateNodeId();
        nodes.push({
          id: materialNodeId,
          name: material.ingredient,
          component_name: material.component,
          description: material.description,
          node_type: 'material',
          location: {
            city: supplierLocation.district,
            country: supplierLocation.country,
          },
          locally_procured: material.locallyProcured,
          emissions_factor: {
            activity_name: emissionsFactorMatch.activityName,
            reference_product_name: emissionsFactorMatch.referenceProduct,
            geography: emissionsFactorMatch.geography,
            source: emissionsFactorMatch.source,
          },
          amount: material.weight,
          unit: material.weightUnit,
          quantity: 1,
          supplier_id: supplier.createSupplier.id,
        })

        //append transport nodes
        const transportNodeId = generateNodeId();
        nodes.push({
          id: transportNodeId,
          name: `${material.ingredient} transportation`,
          node_type: 'transportation',
          location: {
            city: factoryLocation.district,
            country: factoryLocation.country,
          },
          quantity: 1,
        })

        edges.push({
          from_node_id: materialNodeId,
          to_node_id: transportNodeId,
        })
      }
    }

    for (const packaging of packagingTableDataSource) {
      const supplierLocation = JSON.parse(packaging.dataField_supplierLocation)

      const supplier = await handleCreateSupplier(packaging)

      let emissionsFactorMatch = packaging.emissionsFactorMatch

      try {
        if (emissionsFactorMatch?.modified) {
          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        }
      } catch (error) {
        console.error('Error creating emissions factor:', error)
      } finally {
        const packagingNodeId = generateNodeId();
        nodes.push({
          id: packagingNodeId,
          name: packaging.material,
          component_name: packaging.component,
          description: packaging.description,
          node_type: 'packaging',
          packaging_level: packaging.tier,
          recycled_content_rate: packaging.recycledContent,
          location: {
            city: supplierLocation.district,
            country: supplierLocation.country,
          },
          locally_procured: packaging.locallyProcured,
          emissions_factor: {
            activity_name: emissionsFactorMatch.activityName,
            reference_product_name: emissionsFactorMatch.referenceProduct,
            geography: emissionsFactorMatch.geography,
            source: emissionsFactorMatch.source,
          },
          amount: packaging.weight,
          unit: packaging.weightUnit,
          quantity: 1,
          supplier_id: supplier.createSupplier.id,
        })

        //append transport nodes
        const transportNodeId = generateNodeId();
        nodes.push({
          id: transportNodeId,
          name: `${packaging.material} transportation`,
          node_type: 'transportation',
          location: {
            city: factoryLocation.district,
            country: factoryLocation.country,
          },
          quantity: 1,
        })

        edges.push({
          from_node_id: packagingNodeId,
          to_node_id: transportNodeId,
        })
      }
    }

    for (const manufacturingProcess of manufacturingMethodTableDataSource) {
      let emissionsFactorMatch = manufacturingProcess.emissionsFactorMatch

      try {
        if (!emissionsFactorMatch && manufacturingProcess.amount) {
          emissionsFactorMatch = await getManufacturingEF(manufacturingProcess)

          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch,
            'Manufacturing'
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        } else if (emissionsFactorMatch?.modified) {
          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch,
            'Manufacturing'
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        }
      } catch (error) {
        console.error('Error creating emissions factor:', error)
      } finally {
        const manufacturingNodeId = generateNodeId();
        nodes.push({
          id: manufacturingNodeId,
          name: manufacturingProcess.activityName,
          component_name: manufacturingProcess.activityType,
          node_type: 'production',
          location: {
            city: factoryLocation.district,
            country: factoryLocation.country,
          },
          emissions_factor: {
            activity_name: emissionsFactorMatch.activityName,
            reference_product_name: emissionsFactorMatch.referenceProduct,
            geography: emissionsFactorMatch.geography,
            source: emissionsFactorMatch.source,
          },
          quantity: 1,
        })
      }
    }

    const manufacturingNodes = nodes.filter((x) => x.node_type === 'production')
    const transportNodes = nodes.filter((x) => x.node_type === 'transportation')
    for (const transportNode of transportNodes) {
      edges.push({
        from_node_id: transportNode.id,
        to_node_id: manufacturingNodes[0].id,
      })
    }

    for (let i = 0; i < manufacturingNodes.length - 1; i++) {
      edges.push({
        from_node_id: manufacturingNodes[i].id,
        to_node_id: manufacturingNodes[i + 1].id,
      })
    }

    const countryOfUseLocation = JSON.parse(
      addProductForm.getFieldValue('dataField_targetMarketLocation') ??
        productInfo.dataField_targetMarketLocation
    )

    const bundleNodes = processModelData
      ? processModelData.nodes
          .filter((node) => node.nodeType === 'bundle')
          .map((node) => ({
            ...node,
            id: generateNodeId(),
            name: productInfo.productName,
          }))
      : []

    if (!processModelData) {
      const bundleNodeId = generateNodeId();
      bundleNodes.push({
        id: bundleNodeId,
        name: productInfo.productName,
        node_type: 'bundle',
        location: {
          city: factoryLocation.district,
          country: factoryLocation.country,
        },
        quantity: 1,
      })
    }

    const bundleNode = bundleNodes.find(x => x.name == productInfo.productName)

    edges.push({
      from_node_id: manufacturingNodes[manufacturingNodes.length - 1].id,
      to_node_id: bundleNode.id,
    })

    const factoryToRetailId = generateNodeId();
    nodes.push({
      id: factoryToRetailId,
      name: 'Factory to Retail',
      node_type: 'transportation',
      location: {
        city: countryOfUseLocation.district,
        country: countryOfUseLocation.country,
      },
      quantity: 1,
    })

    for (const consumerUse of consumerUseTableDataSource) {
      let emissionsFactorMatch = consumerUse.emissionsFactorMatch

      try {
        if (emissionsFactorMatch?.modified) {
          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch,
            'Consumer Use'
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        }
      } catch (error) {
        console.error('Error creating emissions factor:', error)
      } finally {
        const consumerUseNodeId = generateNodeId();
        nodes.push({
          id: consumerUseNodeId,
          name: consumerUse.resource,
          node_type: 'use',
          amount: consumerUse.amount,
          unit: consumerUse.unit,
          quantity: 1,
          location: {
            city: countryOfUseLocation.district,
            country: countryOfUseLocation.country,
          },
          emissions_factor: {
            activity_name: emissionsFactorMatch.activityName,
            reference_product_name: emissionsFactorMatch.referenceProduct,
            geography: emissionsFactorMatch.geography,
            source: emissionsFactorMatch.source,
          },
        })
      }
    }

    if (!consumerUseTableDataSource.length) {
      const consumerUseNodeId = generateNodeId();
      nodes.push({
        id: consumerUseNodeId,
        name: 'Consumer Use',
        node_type: 'use',
        amount: 0,
        quantity: 1,
        unit: 'kg',
        location: {
          city: countryOfUseLocation.district,
          country: countryOfUseLocation.country,
        },
        emissions_factor: {
          activity_name: 'water production, deionised',
          reference_product_name: 'water, deionised',
          geography: 'RoW',
          source: 'Ecoinvent 3.11',
        },
      })
    }

    const eolNodeId = generateNodeId();
    nodes.push({
      id: eolNodeId,
      name: 'End of Life',
      node_type: 'eol',
      amount: 0,
      quantity: 1,
      unit: 'kg',
      location: {
        city: countryOfUseLocation.district,
        country: countryOfUseLocation.country,
      },
    })

    bundleNodes.forEach((bundleNode) => {
      nodes.push({
        id: bundleNode.id,
        name: bundleNode.name,
        node_type: 'bundle',
        location: {
          city: bundleNode.location.city,
          country: bundleNode.location.country,
        },
        quantity: 1,
      })
    })

    const rootNode = bundleNodes.find(
      (node) => !edges.some((edge) => edge.from_node_id === node.id)
    )

    if (!rootNode) {
      throw new Error('Root node not found in process model')
    }

    edges.push({
      from_node_id: rootNode.id,
      to_node_id: factoryToRetailId,
    })

    const consumerUseNodes = nodes.filter((node) => node.node_type === 'use')
    edges.push({
      from_node_id: factoryToRetailId,
      to_node_id: consumerUseNodes[0].id,
    })

    for (let i = 0; i < consumerUseNodes.length - 1; i++) {
      edges.push({
        from_node_id: consumerUseNodes[i].id,
        to_node_id: consumerUseNodes[i + 1].id,
      })
    }

    edges.push({
      from_node_id: edges[edges.length - 1].to_node_id,
      to_node_id: eolNodeId,
    })

    nodes.map((node) => {
      if (['material', 'packaging'].includes(node.node_type)) {
        if (node.locally_procured) {
          const destinationNode = getDescendantNodeOfType({
            nodes,
            edges,
            node,
            nodeTypes:  ['production', 'bundle', 'use', 'eol'],
            nodeTypeField: "node_type",
          })
          if (destinationNode) {
            node.location = destinationNode.location
          }
        }
        delete node.locally_procured
      }

      return node
    })

    const response = await createProductProcessModel({
      variables: {
        productId: productId,
        processModel: {
          nodes: nodes,
          edges: edges,
        },
      },
    })

    return response.data
  }

  const addProduct = async (productInfo) => {
    let productId = null
    try {
      if (editMode) {
        await backupProduct({
          variables: {
            productId: _productDetails.productId,
          },
        })
      }

      const productResponse = await handleCreateProduct({
        product: productInfo,
        oldProductId: editMode ? _productDetails?.productId : null,
      })
      productId = productResponse.createProduct.productId

      await handleCreateProcessModel(productId)

      await handleActivateProduct(productId)

      if (!userMetadata.user.metadata?.milestone_createProduct)
        await logCreateProduct()

      notification.success({
        placement: 'topRight',
        message: editMode
          ? `Updated ${component ? 'Component' : 'Product'}`
          : `Created ${component ? 'Component' : 'Product'}`,
        description: `${component ? 'Component' : 'Product'} ${
          productResponse.createProduct.productName
        } ${editMode ? 'updated' : 'created'} successfully`,
        duration: 0,
      })

      if (editMode) {
        await deleteProduct({
          variables: {
            productId: `${_productDetails.productId}_backup`,
          },
        })
      }

      setTimeout(() => {
        navigate(routes.products())
      }, 2000)
    } catch (error) {
      console.error(error)
      if (productId) {
        await deleteProduct({
          variables: { productId: productId },
        })
      }

      if (editMode) {
        await restoreProduct({
          variables: {
            productId: `${_productDetails.productId}_backup`,
          },
        })
      }

      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message

      return notification.error({
        placement: 'topRight',
        message: editMode ? 'Error updating product' : 'Error creating product',
        description: errorMessage,
        duration: 0,
      })
    }
  }

  const addComponent = async () => {
    if (!componentDetails) {
      message.error('No components to create')
      return
    }

    addComponentForm.validateFields()

    const productName = addComponentForm.getFieldValue('productName')
    if (!productName?.trim()?.length) {
      message.error('Please provide a product name')
      return
    }

    const _createProduct = async (component) => {
      const createProductInput = {
        product_id: component.component_id ?? Date.now().toString(),
        product_name: component.component_name.trim(),
        product_type: 'component',
        tags: productName.split(',').map((tag) => tag.trim()),
      }

      const response = await createProduct({
        variables: {
          product: createProductInput,
        },
      })

      return response.data
    }

    const _createProcessModel = async (productId, component) => {
      const nodes = []
      const edges = component.edges

      for (const node of component.nodes) {
        let emissionsFactorMatch = node.emissions_factor
        try {
          if (emissionsFactorMatch?.modified) {
            const emissionsFactor = await handleCreateEmissionsFactor(
              emissionsFactorMatch
            )
            if (emissionsFactor.createEmissionsFactor) {
              emissionsFactorMatch = emissionsFactor.createEmissionsFactor
            }
          }
        } catch (error) {
          console.error('Error creating emissions factor:', error)
        } finally {
          nodes.push({
            ...node,
            emissions_factor: emissionsFactorMatch ? {
              activity_name: emissionsFactorMatch.activityName,
              reference_product_name: emissionsFactorMatch.referenceProduct,
              geography: emissionsFactorMatch.geography,
              source: emissionsFactorMatch.source,
            } : null,
          })
        }
      }
      const response = await createProductProcessModel({
        variables: {
          productId: productId,
          processModel: {
            nodes: nodes,
            edges: edges,
          },
        },
      })

      return response.data
    }

    for (const component of componentDetails) {
      let productId = null
      try {
        const productResponse = await _createProduct(component)
        productId = productResponse.createProduct.productId

        await _createProcessModel(productId, component)

        await handleActivateProduct(productId)
      } catch (error) {
        if (productId) {
          await deleteProduct({
            variables: { productId: productId },
          })
        }

        const errorMessage =
          error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
          error.message

        return notification.error({
          placement: 'topRight',
          message: 'Error creating component',
          description: errorMessage,
          duration: 0,
        })
      }
    }

    notification.success({
      placement: 'topRight',
      message: 'Components created successfully',
      duration: 0,
    })

    setTimeout(() => {
      navigate(routes.component())
    }, 2000)
  }

  const addParts = async () => {
    if (!componentTableDataSource.length) {
      message.error('No parts to create')
      return
    }

    const productName = document.getElementById(
      'add-component-product-name'
    ).value
    if (!productName?.trim()?.length) {
      message.error('Please provide a product name')
      return
    }

    const _createProduct = async () => {
      const createProductInput = {
        product_id: Date.now().toString(),
        product_name: productName,
        product_type: 'product',
        brand: orgName,
        country_of_use: 'United States',
        primary_category: 'Office Chairs',
      }

      const response = await createProduct({
        variables: {
          product: createProductInput,
        },
      })

      return response.data
    }

    const _createProcessModel = async (productId, component) => {
      const response = await createProductProcessModel({
        variables: {
          productId: productId,
          processModel: {
            nodes: component.nodes,
            edges: component.edges,
          },
        },
      })

      return response.data
    }

    try {

      const nodes = [];
      const edges = [];
      let lastNodes = [];

      for (const component of componentTableDataSource) {

        for (const node of component.nodes) {
          nodes.push(transformNode({
            ...node,
            quantity: ["material", "packaging"].includes(node.nodeType) ? (node.quantity * component.quantity) : node.quantity,
          }))
        }

        for (const edge of component.edges) {
          edges.push({
            from_node_id: edge.fromNodeId,
            to_node_id: edge.toNodeId,
          })
        }

        const lastNode = findLastNode(component.nodes, component.edges, "bundle")
        if (!lastNode) {
          throw new Error(`No last node found for component ${component.componentId}`)
        }
        lastNodes.push(lastNode)
      }

      const generateNodeId = (() => {
        let maxNodeId = Math.max(...nodes.map(node => node.id)) + 1;
        return () => maxNodeId++;
      })();

      const assemblyNodeId = generateNodeId()
      const factoryToRetailNodeId = generateNodeId()
      const consumerUseNodeId = generateNodeId()
      const eolNodeId = generateNodeId()

      // Add default assembly bundle node
      nodes.push({
        id:assemblyNodeId,
        name: 'Product Assembly',
        node_type: 'bundle',
        location: {
          city: 'Timbuktu',
          country: 'Mali',
        },
        quantity: 1,
      })

      //Add factory to retail
      nodes.push({
        id: factoryToRetailNodeId,
        name: 'Factory to Retail',
        node_type: 'transportation',
        location: null,
        quantity: 1,
      })

      // Add default use node
      nodes.push({
        id: consumerUseNodeId,
        name: 'Consumer Use',
        node_type: 'use',
        amount: 0,
        quantity: 1,
        unit: 'kg',
        location: {
          city: 'Washington DC',
          country: 'United States',
        },
        emissions_factor: {
          activity_name: 'water production, deionised',
          reference_product_name: 'water, deionised',
          geography: 'RoW',
          source: 'Ecoinvent 3.11',
        },
      })

      // Add default end of life node
      nodes.push({
        id: eolNodeId,
        name: 'End of Life',
        node_type: 'eol',
        amount: 0,
        quantity: 1,
        unit: 'kg',
        location: {
          city: 'Washington DC',
          country: 'United States',
        },
      })

      const bundleNodes = lastNodes.filter(node => node.nodeType === 'bundle')
      for (const bundleNode of bundleNodes) {
        if (bundleNode.id === assemblyNodeId) {
          continue
        }

        const transportNodeId = generateNodeId()
        nodes.push({
          id: transportNodeId,
          name: `${bundleNode.name} transportation`,
          node_type: 'transportation',
          location: null,
          quantity: 1,
        })

        edges.push({
          from_node_id: bundleNode.id,
          to_node_id: transportNodeId,
        })

        edges.push({
          from_node_id: transportNodeId,
          to_node_id: assemblyNodeId,
        })
      }

      edges.push(
        {
          from_node_id: assemblyNodeId,
          to_node_id: factoryToRetailNodeId,
        },
        {
          from_node_id: factoryToRetailNodeId,
          to_node_id: consumerUseNodeId,
        },
        {
          from_node_id: consumerUseNodeId,
          to_node_id: eolNodeId,
        }
      )

      let productId = null

      const productResponse = await _createProduct()
      productId = productResponse.createProduct.productId

      await _createProcessModel(productId, { nodes, edges })

      await handleActivateProduct(productId)

      notification.success({
        placement: 'topRight',
        message: 'Product created successfully',
        duration: 0,
      })

      setTimeout(() => {
        navigate(routes.products())
      }, 2000)
    } catch (error) {
      if (productId) {
        await deleteProduct({
          variables: { productId: productId },
        })
      }

      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message

      return notification.error({
        placement: 'topRight',
        message: 'Error creating product from parts list',
        description: errorMessage,
        duration: 0,
      })
    }
  }

  const handleFactoryLocationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setProductInfoFactoryLocationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value)
      setMapboxAutocompleteOptions(placesData)
    } catch (error) {
      message.error('Failed to fetch factory locations')
      setMapboxAutocompleteOptions([])
    } finally {
      setProductInfoFactoryLocationMapboxDataLoading(false)
    }
  }

  const handleTargetMarketLocationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setProductInfoFactoryLocationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value, '', 'country')
      setMapboxAutocompleteOptions(placesData)
    } catch (error) {
      message.error('Failed to fetch factory locations')
      setMapboxAutocompleteOptions([])
    } finally {
      setProductInfoFactoryLocationMapboxDataLoading(false)
    }
  }

  const handleIngredientSuppierLocationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setIngredientSupplierLocationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value)
      setMapboxAutocompleteOptions(placesData)
    } catch (error) {
      message.error('Failed to fetch supplier locations')
      setMapboxAutocompleteOptions([])
    } finally {
      setIngredientSupplierLocationMapboxDataLoading(false)
    }
  }

  const handlePackagingSuppierLocationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setPackagingSupplierLocationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value)
      setMapboxAutocompleteOptions(placesData)
    } catch (error) {
      message.error('Failed to fetch supplier locations')
      setMapboxAutocompleteOptions([])
    } finally {
      setPackagingSupplierLocationMapboxDataLoading(false)
    }
  }

  const handleTransportSegmentDestinationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setTransportSegmentDestinationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value, '', 'country')
      setMapboxAutocompleteOptions(
        placesData.map((place) => {
          return {
            ...place,
            label: place.label + ' (Average)',
          }
        })
      )
    } catch (error) {
      message.error('Failed to fetch transport segment destinations')
      setMapboxAutocompleteOptions([])
    } finally {
      setTransportSegmentDestinationMapboxDataLoading(false)
    }
  }

  const handlePredictIngredientSource = async (ingredient, productCategory) => {
    const factoryLocation = JSON.parse(
      addProductForm.getFieldValue('dataField_factoryLocation') ??
        productInfo?.dataField_factoryLocation ??
        null
    )
    if (!factoryLocation?.country) {
      return
    }

    const predictedIngredientSource = {
      country: factoryLocation.country,
      district: null,
      latitude: factoryLocation.latitude,
      longitude: factoryLocation.longitude,
      country_code: factoryLocation.country_code,
      predicted: false,
      locallyProcured: false,
    }

    try {
      const response = await predictIngredientSource({
        variables: {
          ingredientName: ingredient.ingredient,
          productCategory: productCategory,
          country: factoryLocation.country,
        },
      })

      if (response?.data?.predictIngredientSource) {
        predictedIngredientSource.country =
          response.data.predictIngredientSource.country
        predictedIngredientSource.district = null
        predictedIngredientSource.latitude =
          response.data.predictIngredientSource.latitude
        predictedIngredientSource.longitude =
          response.data.predictIngredientSource.longitude
        predictedIngredientSource.country_code =
          response.data.predictIngredientSource.countryCode
        predictedIngredientSource.predicted = true
        predictedIngredientSource.locallyProcured =
          response.data.predictIngredientSource.locallyProcured
      }
    } catch (error) {
      console.error('Error predicting ingredient source:', error)
    } finally {
      setIngredientTableDataSource((prevDataSource) =>
        prevDataSource.map((_ingredient) => {
          if (_ingredient.key === ingredient.key) {
            if (predictedIngredientSource.country) {
              _ingredient.supplierOrigin = predictedIngredientSource.country
              _ingredient.locallyProcured = predictedIngredientSource.locallyProcured
              _ingredient.dataField_supplierLocation = JSON.stringify({
                country: predictedIngredientSource.country,
                district: predictedIngredientSource.district,
                latitude: predictedIngredientSource.latitude,
                longitude: predictedIngredientSource.longitude,
                country_code: predictedIngredientSource.country_code,
                predicted: predictedIngredientSource.predicted,
              })
            }

            _ingredient.predictSourceIsLoading = false
            _ingredient.emissionsFactorMatch = null
            _ingredient.predictEmissionsFactorMatchIsLoading = true
            handlePredictIngredientEmissionsFactors(
              _ingredient,
              productCategory
            )
          }

          return _ingredient
        })
      )
      setAddIngredientTableKey(uuidv4())
    }
  }

  const handlePredictPackagingSource = async (packaging) => {
    const factoryLocation = JSON.parse(
      addProductForm.getFieldValue('dataField_factoryLocation') ??
        productInfo?.dataField_factoryLocation ??
        null
    )
    if (!factoryLocation?.country) {
      return
    }

    const predictedPackagingSource = {
      country: factoryLocation.country,
      district: null,
      latitude: factoryLocation.latitude,
      longitude: factoryLocation.longitude,
      country_code: factoryLocation.country_code,
      predicted: false,
      locallyProcured: false,
    }

    try {
      const response = await predictIngredientSource({
        variables: {
          ingredientName: packaging.material,
          country: factoryLocation.country,
        },
      })

      if (response?.data?.predictIngredientSource) {
        predictedPackagingSource.country =
          response.data.predictIngredientSource.country
        predictedPackagingSource.district = null
        predictedPackagingSource.latitude =
          response.data.predictIngredientSource.latitude
        predictedPackagingSource.longitude =
          response.data.predictIngredientSource.longitude
        predictedPackagingSource.country_code =
          response.data.predictIngredientSource.countryCode
        predictedPackagingSource.predicted = true
        predictedPackagingSource.locallyProcured =
          response.data.predictIngredientSource.locallyProcured
      }
    } catch (error) {
      console.error('Error predicting packaging source:', error)
    } finally {
      setPackagingTableDataSource((prevDataSource) =>
        prevDataSource.map((_packaging) => {
          if (_packaging.key === packaging.key) {
            if (predictedPackagingSource.country) {
              _packaging.supplierOrigin = predictedPackagingSource.country
              _packaging.locallyProcured = predictedPackagingSource.locallyProcured
              _packaging.dataField_supplierLocation = JSON.stringify({
                country: predictedPackagingSource.country,
                district: predictedPackagingSource.district,
                latitude: predictedPackagingSource.latitude,
                longitude: predictedPackagingSource.longitude,
                country_code: predictedPackagingSource.country_code,
                predicted: predictedPackagingSource.predicted,
              })
            }

            _packaging.predictSourceIsLoading = false
            _packaging.emissionsFactorMatch = null
            _packaging.predictEmissionsFactorMatchIsLoading = true
            handlePredictPackagingEmissionsFactors(_packaging)
          }

          return _packaging
        })
      )
      setAddPackagingTableKey(uuidv4())
    }
  }

  const handlePredictEmissionsFactors = async ({
    chemicalName,
    productCategory = null,
    casNo = null,
    geography,
    geographyModeling = false,
    unit = null,
  }) => {
    const emissionsFactorMatch = {
      activityMatch: null,
      activityMatches: [],
    }

    const response = await predictEmissionsFactors({
      variables: {
        chemicalName: chemicalName,
        productCategory: productCategory,
        casNo: casNo,
        geography: geography,
        geographyModeling: geographyModeling,
        unit: unit,
      },
    })

    if (response?.data?.predictEmissionsFactors?.recommendations?.length) {
      emissionsFactorMatch.activityMatch = {
        ...(response?.data?.predictEmissionsFactors?.matchedActivity ??
          emissionsFactorMatch.activityMatches[0]),
        ...(response?.data?.predictEmissionsFactors?.confidence && {
          confidence: response.data.predictEmissionsFactors.confidence,
        }),
        ...(response?.data?.predictEmissionsFactors?.explanation && {
          explanation: response.data.predictEmissionsFactors.explanation,
        }),
        exchanges:
          response.data.predictEmissionsFactors?.matchedActivity?.exchanges ??
          [],
        elementalEfValues: response.data.predictEmissionsFactors.matchedActivity?.elementalEfValues,
      }

      emissionsFactorMatch.activityMatches =
        response?.data?.predictEmissionsFactors.recommendations

      const matchExists = emissionsFactorMatch.activityMatches.some(
        (match) =>
          match.activityName ===
            emissionsFactorMatch.activityMatch.activityName &&
          match.referenceProduct ===
            emissionsFactorMatch.activityMatch.referenceProduct &&
          match.source === emissionsFactorMatch.activityMatch.source &&
          match.geography === emissionsFactorMatch.activityMatch.geography &&
          JSON.stringify(match.exchanges) ===
            JSON.stringify(emissionsFactorMatch.activityMatch.exchanges)
      )

      if (!matchExists) {
        emissionsFactorMatch.activityMatches = [
          emissionsFactorMatch.activityMatch,
          ...emissionsFactorMatch.activityMatches,
        ]
      }
    }
    return emissionsFactorMatch
  }

  const handlePredictIngredientEmissionsFactors = async (
    ingredient,
    productCategory
  ) => {
    let emissionsFactorMatch = null
    let recycledEmissionsFactorMatch = null

    try {
      if (
        ingredient.emissionsFactorMatch &&
        ingredient.ingredient === ingredient.emissionsFactorMatch.chemicalName
      ) {
        return
      }
      let emissionsFactorMatchGeography = 'GLO'
      if (ingredient.dataField_supplierLocation) {
        const supplierLocation = JSON.parse(
          ingredient.dataField_supplierLocation
        )
        emissionsFactorMatchGeography = supplierLocation.country_code
      }

      const ingredientName = `${ingredient.ingredient}${ingredient.component ? ` (${ingredient.component})` : ''}${ingredient.description ? ` (${ingredient.description})` : ''}`

      emissionsFactorMatch = await handlePredictEmissionsFactors({
        chemicalName: ingredientName,
        productCategory: productCategory,
        casNo: ingredient.casNo,
        geography: emissionsFactorMatchGeography,
        geographyModeling: true,
        unit: 'g',
      })

      if (ingredient.recycledContent) {
        recycledEmissionsFactorMatch = await handlePredictEmissionsFactors({
          chemicalName: `${ingredientName} from recycled content`,
          productCategory: productCategory,
          casNo: ingredient.casNo,
          geography: emissionsFactorMatchGeography,
          geographyModeling: true,
          unit: 'g',
        })
      }
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setIngredientTableDataSource((prevDataSource) =>
        prevDataSource.map((_ingredient) => {
          if (_ingredient.key === ingredient.key) {
            if (emissionsFactorMatch?.activityMatches.length) {
              _ingredient.emissionsFactorMatches = []
              if (!ingredient.emissionsFactorMatch) {
                _ingredient.emissionsFactorMatch =
                  emissionsFactorMatch.activityMatch
                _ingredient.emissionsFactorMatchActivityName =
                  emissionsFactorMatch.activityMatch.activityName
                _ingredient.emissionsFactorMatchSource =
                  emissionsFactorMatch.activityMatch.source
              } else {
                const matchedActivity =
                  emissionsFactorMatch.activityMatches.find(
                    (activity) =>
                      activity.activityName ===
                        ingredient.emissionsFactorMatch.activityName &&
                      activity.referenceProduct ===
                        ingredient.emissionsFactorMatch.referenceProduct &&
                      activity.source ===
                        ingredient.emissionsFactorMatch.source &&
                      activity.geography ===
                        ingredient.emissionsFactorMatch.geography
                  )

                if (!matchedActivity) {
                  _ingredient.emissionsFactorMatches.push(
                    ingredient.emissionsFactorMatch
                  )
                }
              }

              _ingredient.emissionsFactorMatches =
                _ingredient.emissionsFactorMatches.concat(
                  emissionsFactorMatch.activityMatches
                )
            }
            _ingredient.predictEmissionsFactorMatchIsLoading = false

            if (recycledEmissionsFactorMatch?.activityMatches?.length) {
              _ingredient.recycledEmissionsFactorMatch =
                recycledEmissionsFactorMatch.activityMatch
              _ingredient.recycledEmissionsFactorMatches =
                recycledEmissionsFactorMatch.activityMatches
              _ingredient.recycledEmissionsFactorMatchActivityName =
                recycledEmissionsFactorMatch.activityMatch.activityName
              _ingredient.recycledEmissionsFactorMatchSource =
                recycledEmissionsFactorMatch.activityMatch.source
            }
          }

          return _ingredient
        })
      )
      setAddIngredientTableKey(uuidv4())
    }
  }

  const handlePredictPackagingEmissionsFactors = async (packaging) => {
    let emissionsFactorMatch = null

    let recycledEmissionsFactorMatch = null

    try {
      if (
        packaging.emissionsFactorMatch &&
        packaging.material === packaging.emissionsFactorMatch.chemicalName
      ) {
        return
      }
      let emissionsFactorMatchGeography = 'GLO'
      if (packaging.dataField_supplierLocation) {
        const supplierLocation = JSON.parse(
          packaging.dataField_supplierLocation
        )
        emissionsFactorMatchGeography = supplierLocation.country_code
      }

      emissionsFactorMatch = await handlePredictEmissionsFactors({
        chemicalName: packaging.material,
        geography: emissionsFactorMatchGeography,
        geographyModeling: true,
        unit: 'g',
      })

      if (packaging.recycledContent) {
        recycledEmissionsFactorMatch = await handlePredictEmissionsFactors({
          chemicalName: packaging.material,
          geography: emissionsFactorMatchGeography,
          geographyModeling: true,
          unit: 'g',
        })
      }
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setPackagingTableDataSource((prevDataSource) =>
        prevDataSource.map((_packaging) => {
          if (_packaging.key === packaging.key) {
            if (emissionsFactorMatch?.activityMatches.length) {
              _packaging.emissionsFactorMatches = []
              if (!packaging.emissionsFactorMatch) {
                _packaging.emissionsFactorMatch =
                  emissionsFactorMatch.activityMatch
                _packaging.emissionsFactorMatchActivityName =
                  emissionsFactorMatch.activityMatch.activityName
                _packaging.emissionsFactorMatchSource =
                  emissionsFactorMatch.activityMatch.source
              } else {
                const matchedActivity =
                  emissionsFactorMatch.activityMatches.find(
                    (activity) =>
                      activity.activityName ===
                        packaging.emissionsFactorMatch.activityName &&
                      activity.referenceProduct ===
                        packaging.emissionsFactorMatch.referenceProduct &&
                      activity.source ===
                        packaging.emissionsFactorMatch.source &&
                      activity.geography ===
                        packaging.emissionsFactorMatch.geography
                  )

                if (!matchedActivity) {
                  _packaging.emissionsFactorMatches.push(
                    packaging.emissionsFactorMatch
                  )
                }
              }

              _packaging.emissionsFactorMatches =
                _packaging.emissionsFactorMatches?.concat(
                  emissionsFactorMatch.activityMatches
                )
            }
            _packaging.predictEmissionsFactorMatchIsLoading = false


            if (recycledEmissionsFactorMatch?.activityMatches?.length) {
              _packaging.recycledEmissionsFactorMatch =
                recycledEmissionsFactorMatch.activityMatch
              _packaging.recycledEmissionsFactorMatches =
                recycledEmissionsFactorMatch.activityMatches
              _packaging.recycledEmissionsFactorMatchActivityName =
                recycledEmissionsFactorMatch.activityMatch.activityName
              _packaging.recycledEmissionsFactorMatchSource =
                recycledEmissionsFactorMatch.activityMatch.source
            }
          }

          return _packaging
        })
      )
      setAddPackagingTableKey(uuidv4())
    }
  }

  const handlePredictManufacturingEmissionsFactors = async (manufacturing) => {
    let emissionsFactorMatch = null

    try {
      if (
        manufacturing.emissionsFactorMatch &&
        manufacturing.activityName ===
          manufacturing.emissionsFactorMatch.chemicalName
      ) {
        return
      }

      let emissionsFactorMatchGeography = 'GLO'
      const factoryLocation = JSON.parse(
        addProductForm.getFieldValue('dataField_factoryLocation') ??
          productInfo.dataField_factoryLocation
      )
      if (factoryLocation && factoryLocation.country_code)
        emissionsFactorMatchGeography = factoryLocation.country_code

      emissionsFactorMatch = await handlePredictEmissionsFactors({
        chemicalName: manufacturing.activityName,
        geography: emissionsFactorMatchGeography,
        geographyModeling: true,
        unit: 'g',
      })
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setManufacturingMethodTableDataSource((prevDataSource) =>
        prevDataSource.map((_manufacturing) => {
          if (_manufacturing.key === manufacturing.key) {
            if (emissionsFactorMatch?.activityMatches.length) {
              _manufacturing.emissionsFactorMatches = []
              if (!manufacturing.emissionsFactorMatch) {
                _manufacturing.emissionsFactorMatch =
                  emissionsFactorMatch.activityMatch
                _manufacturing.emissionsFactorMatchActivityName =
                  emissionsFactorMatch.activityMatch.activityName
                _manufacturing.emissionsFactorMatchSource =
                  emissionsFactorMatch.activityMatch.source
              } else {
                const matchedActivity =
                  emissionsFactorMatch.activityMatches.find(
                    (activity) =>
                      activity.activityName ===
                        manufacturing.emissionsFactorMatch.activityName &&
                      activity.referenceProduct ===
                        manufacturing.emissionsFactorMatch.referenceProduct &&
                      activity.source ===
                        manufacturing.emissionsFactorMatch.source &&
                      activity.geography ===
                        manufacturing.emissionsFactorMatch.geography
                  )

                if (!matchedActivity) {
                  _manufacturing.emissionsFactorMatches.push(
                    manufacturing.emissionsFactorMatch
                  )
                }
              }

              _manufacturing.emissionsFactorMatches =
                _manufacturing.emissionsFactorMatches.concat(
                  emissionsFactorMatch.activityMatches
                )
            }
            _manufacturing.predictEmissionsFactorMatchIsLoading = false
          }
          return _manufacturing
        })
      )
      setAddManufacturingProcessTableKey(uuidv4())
    }
  }

  const handlePredictConsumerUseEmissionsFactors = async (consumerUse) => {
    let emissionsFactorMatch = null

    try {
      if (
        consumerUse.emissionsFactorMatch &&
        consumerUse.resource === consumerUse.emissionsFactorMatch.chemicalName
      ) {
        return
      }

      let emissionsFactorMatchGeography = 'GLO'
      const countryOfUseLocation = JSON.parse(
        addProductForm.getFieldValue('dataField_targetMarketLocation') ??
          productInfo.dataField_targetMarketLocation
      )
      if (countryOfUseLocation && countryOfUseLocation.country_code)
        emissionsFactorMatchGeography = countryOfUseLocation.country_code

      emissionsFactorMatch = await handlePredictEmissionsFactors({
        chemicalName: consumerUse.resource,
        geography: emissionsFactorMatchGeography,
        geographyModeling: true,
        unit: consumerUse.unit ?? 'g',
      })
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setConsumerUseTableDataSource((prevDataSource) =>
        prevDataSource.map((_consumerUse) => {
          if (_consumerUse.key === consumerUse.key) {
            if (emissionsFactorMatch?.activityMatches?.length) {
              _consumerUse.emissionsFactorMatch =
                emissionsFactorMatch.activityMatch
              _consumerUse.emissionsFactorMatches =
                emissionsFactorMatch.activityMatches
              _consumerUse.emissionsFactorMatchActivityName =
                emissionsFactorMatch.activityMatch.activityName
              _consumerUse.emissionsFactorMatchSource =
                emissionsFactorMatch.activityMatch.source
            }

            _consumerUse.predictEmissionsFactorMatchIsLoading = false
          }

          return _consumerUse
        })
      )
      setConsumerUseTableKey(uuidv4())
    }
  }

  const handleFilterEmissionsFactorMatchesByGeography = async (geography) => {
    setPredictIngredientEmissionsFactorsLoading(true)
    try {
      const emissionsFactorMatch = await handlePredictEmissionsFactors({
        chemicalName: selectedChemicalName.chemicalName,
        productCategory: productInfo.category,
        casNo: selectedChemicalName.casNo,
        geography: geography,
      })

      if (emissionsFactorMatch?.activityMatches.length) {
        await setSelectedActivity({
          activityName: emissionsFactorMatch.activityMatch.activityName,
          referenceProduct: emissionsFactorMatch.activityMatch.referenceProduct,
          geography: emissionsFactorMatch.activityMatch.geography,
          source: emissionsFactorMatch.activityMatch.source,
          unit: emissionsFactorMatch.activityMatch.unit,
        })

        setEmissionsFactorsActivityMatchesTableDataSource(
          emissionsFactorMatch.activityMatches
        )
        setEmissionsFactorsActivityMatchesTableKey(uuidv4())
      }
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setPredictIngredientEmissionsFactorsLoading(false)
    }
  }

  const populatePredictedPackagingMaterials = async () => {
    const contentWeight = ingredientTableDataSource.reduce(
      (totalWeight, ingredient) => totalWeight + ingredient.weight,
      0
    )

    await predictProductPackaging({
      variables: {
        productCategory: productInfo.category,
        weight: contentWeight,
      },
    })
  }

  // Helper function to check if the current tenant is a non-carbon tenant
  const isNonCarbonTenant = (orgMetadata) => {
    return orgMetadata?.tenantType === 'non-carbon'
  }

  // TODO: Clean up this function
  const preferredManufacturingActivityName = {
    'Shopping Totes': 'Garment Cut and Sew',
    default: 'Cleaning Products Manufacturing',
  }

  const manufacturingMethodActivityUnitMapping = {
    'Electricity usage': 'kWh',
    Water: 'l',
    'Total Carbon Emissions': 'kg',
  }

  const fetchManufacturingMethods = async () => {
    function getActivityInfoByManufacturingMethod(manufacturingMethod) {
      if (manufacturingMethod.co2eKg) {
        const activityType = 'Total Carbon Emissions'
        return {
          activityType: activityType,
          amount: manufacturingMethod.co2eKg,
          unit: manufacturingMethodActivityUnitMapping[activityType],
        }
      }

      if (manufacturingMethod.electricityKwh) {
        const activityType = 'Electricity usage'
        return {
          activityType: activityType,
          amount: manufacturingMethod.electricityKwh,
          unit: manufacturingMethodActivityUnitMapping[activityType],
        }
      }

      if (manufacturingMethod.waterLiters) {
        const activityType = 'Water'
        return {
          activityType: activityType,
          amount: manufacturingMethod.waterLiters,
          unit: manufacturingMethodActivityUnitMapping[activityType],
        }
      }
    }

    try {
      const response = await getManufacturingMethods()

      let predictedManufacturingMethod =
        preferredManufacturingActivityName.default

      const category = addProductForm.getFieldValue('category')

      // Check tenant type first to determine prediction logic
      if (isNonCarbonTenant(orgMemberInfo?.orgMetadata)) {
        // For non-carbon tenants, use "[category name] manufacturing" format
        if (category && category.trim()) {
          predictedManufacturingMethod = `${category.trim()} manufacturing`
        } else {
          // Fallback if category is empty/null
          predictedManufacturingMethod = preferredManufacturingActivityName.default
        }
      } else {
        // Existing carbon tenant logic
        if (preferredManufacturingActivityName[category]) {
          predictedManufacturingMethod = preferredManufacturingActivityName[category]
        } else {
          const manufacturingProcessPredictions =
            await predictManufacturingProcesses({
              variables: {
                productName: addProductForm.getFieldValue('productName'),
                productCategory: category,
              },
            })

          if (
            manufacturingProcessPredictions?.data
              ?.predictManufacturingProcesses[0]
          ) {
            predictedManufacturingMethod =
              manufacturingProcessPredictions.data
                .predictManufacturingProcesses[0]
          }
        }
      }

      if (response?.data?.getManufacturingMethods) {
        setManufacturingMethods(response.data.getManufacturingMethods)

        const manufacturingActivityNames = []

        const manufacturingMethods = response.data.getManufacturingMethods
          .map((method, index) => {
            manufacturingActivityNames.push({
              option: method.processName,
              value: method.processName,
              co2eKg: ((method.co2eKg ?? 0) / method.amountOfProductKg).toFixed(
                3
              ),
            })

            const activityInfo = getActivityInfoByManufacturingMethod(method)

            return {
              key: uuidv4(),
              activityName: method.processName,
              predictEmissionsFactorMatchIsLoading: true,
              amountOfProductKg: method.amountOfProductKg,
              ...activityInfo,
              amount: 0,
            }
          })
          .filter(
            (method) => method.activityName === predictedManufacturingMethod
          )

        setManufacturingActivityName(manufacturingActivityNames)

        if (!editMode) {
          setManufacturingMethodTableDataSource(manufacturingMethods)
          setAddManufacturingProcessTableKey(uuidv4())

          manufacturingMethods.forEach((manufacturingMethod) => {
            if (manufacturingMethod.activityType === 'Total Carbon Emissions') {
              handlePredictManufacturingEmissionsFactors(manufacturingMethod)
            }
          })
        }
      }
    } catch (error) {
      message.error('Failed to fetch manufacturing methods')
      console.error('Error fetching manufacturing methods:', error)
    }
  }

  const preferredTransportSegments = [
    {
      key: 1,
      segmentName: 'Source to Factory',
      destination: addProductForm.getFieldValue('factoryLocation'),
      dataField_destination: addProductForm.getFieldValue(
        'dataField_factoryLocation'
      ),
    },
    {
      key: 2,
      segmentName: 'Factory to Retail',
      destination: addProductForm.getFieldValue('targetMarketLocation'),
      dataField_destination: addProductForm.getFieldValue(
        'dataField_targetMarketLocation'
      ),
    },
  ]

  const fetchTransportSegments = () => {
    setTransportationTableDataSource(preferredTransportSegments)
    setAddTransportationTableKey(uuidv4())
  }

  const fetchConsumerUse = async () => {
    const countryOfUseLocation = JSON.parse(
      addProductForm.getFieldValue('dataField_targetMarketLocation')
    )

    const predictedConsumerUse = await predictConsumerUse({
      variables: {
        productCategory: addProductForm.getFieldValue('category'),
        geographyIso3: countryOfUseLocation.country_code,
      },
    })

    if (predictedConsumerUse?.data?.predictConsumerUse) {
      const consumerUseData =
        predictedConsumerUse?.data?.predictConsumerUse?.map((consumerUse) => {
          return {
            key: uuidv4(),
            resource: consumerUse.name,
            amount: consumerUse.amount,
            unit: consumerUse.unit,
            predictEmissionsFactorMatchIsLoading: true,
          }
        })
      setConsumerUseTableDataSource(consumerUseData)
      setConsumerUseTableKey(uuidv4())

      for (const consumerUse of consumerUseData) {
        handlePredictConsumerUseEmissionsFactors(consumerUse)
      }
    }
  }

  const prev = () => {
    setCurrentStep(currentStep - 1)
  }

  const addExchangeStepPrev = () => {
    setCurrentAddExchangeStep(currentAddExchangeStep - 1)
  }

  const camelCaseToNormalCase = (str) => {
    return str
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, (c) => c.toUpperCase())
  }

  const handleIngredientSupplierChange = (values: any) => {
    if (values.supplierOrigin) {
      values.predictSourceIsLoading = false
      values.predictEmissionsFactorMatchIsLoading = true
      handlePredictIngredientEmissionsFactors(values, productInfo.category)
      return values
    }

    values.predictSourceIsLoading = true
    values.predictEmissionsFactorMatchIsLoading = false
    handlePredictIngredientSource(values, productInfo.category)
    return values
  }

  const confirmAddIngredient = (values: any) => {
    if (values?.key?.length ?? 0) {
      const updatedDataSource = ingredientTableDataSource.map((ingredient) => {
        if (ingredient.key === values.key) {
          if (
            ingredient.ingredient.trim().toLowerCase() !==
            values.ingredient.trim().toLowerCase()
          ) {
            const existingIngredient = ingredientTableDataSource.find(
              (ingredient) =>
                ingredient.ingredient.trim().toLowerCase() ===
                values.ingredient.trim().toLowerCase()
            )
            if (existingIngredient) {
              message.error(
                `Error: Ingredient (${values.ingredient}) already exists`
              )
              throw Error(
                `Error: Ingredient (${values.ingredient}) already exists`
              )
            }

            delete values.supplierOrigin
            values = handleIngredientSupplierChange(values)
          }
          return { ...ingredient, ...values }
        }
        return ingredient
      })

      setIngredientTableDataSource(updatedDataSource)
    } else {
      const existingIngredient = ingredientTableDataSource.find(
        (ingredient) =>
          ingredient.ingredient.trim().toLowerCase() ===
          values.ingredient.trim().toLowerCase()
      )
      if (existingIngredient) {
        message.error(`Error: Ingredient (${values.ingredient}) already exists`)
        throw Error(`Error: Ingredient (${values.ingredient}) already exists`)
      }

      values.key = uuidv4()
      values = handleIngredientSupplierChange(values)
      setIngredientTableDataSource((prevDataSource) => [
        ...prevDataSource,
        values,
      ])
    }

    setAdvancedAddIngredientFieldsIsChecked(false)
    addIngredientForm.resetFields()
    setIsAddIngredientModalOpen(false)
    setAddIngredientTableKey(uuidv4())
  }

  const confirmAddManufacturing = (values: any) => {
    if (values?.key?.length ?? 0) {
      const updatedDataSource = manufacturingMethodTableDataSource.map(
        (manufacturingMethod) => {
          if (manufacturingMethod.key === values.key) {
            if (values.activityType === 'Total Carbon Emissions') {
              if (values.amount) {
                values.emissionsFactorMatch = null
                values.emissionsFactorMatches = null
                values.emissionsFactorMatchActivityName = null
              } else {
                values.predictEmissionsFactorMatchIsLoading = true
                handlePredictManufacturingEmissionsFactors(values)
              }
            }
            return { ...manufacturingMethod, ...values }
          }
          return manufacturingMethod
        }
      )

      setManufacturingMethodTableDataSource(updatedDataSource)
    } else {
      const existingManufacturingMethod =
        manufacturingMethodTableDataSource.find(
          (manufacturingMethod) =>
            manufacturingMethod.activityName.trim().toLowerCase() ===
              values.activityName.trim().toLowerCase() &&
            manufacturingMethod.activityType.trim().toLowerCase() ===
              values.activityType.trim().toLowerCase()
        )
      if (existingManufacturingMethod) {
        return message.error(
          `Error: Activity Type "${values.activityType}" for Manufacturing method "${values.activityName}" already exists`
        )
      }

      values.key = uuidv4()

      if (values.activityType === 'Total Carbon Emissions' && !values.amount) {
        values.predictEmissionsFactorMatchIsLoading = true
        handlePredictManufacturingEmissionsFactors(values)
      }

      setManufacturingMethodTableDataSource((prevDataSource) => [
        ...prevDataSource,
        values,
      ])
    }

    addManufacturingMethodForm.resetFields()
    setCustomManufacturingEF(false)
    setIsAddManufacturingMethodModalOpen(false)
    setAddManufacturingProcessTableKey(uuidv4())
  }

  const confirmAddTransportSegment = (values: any) => {
    if (values?.key?.length ?? 0) {
      const updatedDataSource = transportationTableDataSource.map(
        (transportSegment) => {
          if (transportSegment.key === values.key) {
            return { ...transportSegment, ...values }
          }
          return transportSegment
        }
      )

      setTransportationTableDataSource(updatedDataSource)
    } else {
      values.key = uuidv4()
      setTransportationTableDataSource((prevDataSource) => [
        ...prevDataSource,
        values,
      ])
    }

    addTransportationForm.resetFields()
    setIsAddTransportationModalOpen(false)
    setAddTransportationTableKey(uuidv4())
  }

  const handleEditIngredient = (record: any) => {
    addIngredientForm.resetFields()
    setAdvancedAddIngredientFieldsIsChecked(false)
    addIngredientForm.setFieldsValue(record)
    if (record['dataField_advancedFields']) {
      setAdvancedAddIngredientFieldsIsChecked(true)
    }
    setIsAddIngredientModalOpen(true)
  }

  const handleEditManufacturingMethod = (record: any) => {
    addManufacturingMethodForm.resetFields()
    if (record.amount && record.activityType === 'Total Carbon Emissions') {
      setCustomManufacturingEF(true)
    }
    addManufacturingMethodForm.setFieldsValue(record)
    setIsAddManufacturingMethodModalOpen(true)
  }

  const handleEditTransportSegment = (record: any) => {
    addTransportationForm.resetFields()
    addTransportationForm.setFieldsValue(record)
    setIsAddTransportationModalOpen(true)
  }

  const handleDeleteIngredient = (key: number) => {
    confirm({
      title: 'Do you want to delete this ingredient?',
      icon: <DeleteOutlined />,
      onOk() {
        const updatedTableData = ingredientTableDataSource.filter(
          (ingredient) => ingredient.key !== key
        )
        setIngredientTableDataSource(updatedTableData)
        setAddIngredientTableKey(uuidv4())
      },
      onCancel() {
        return false
      },
    })
  }

  const handleShowEmissionsFactorsActivityRecommendations = async (
    record,
    activityType = 'material'
  ) => {

    let geography = 'GLO'
    try {
      let supplierLocation
      supplierLocation = JSON.parse(record.dataField_supplierLocation ?? null)
      if (record.activityName) {
        supplierLocation = JSON.parse(
          addProductForm.getFieldValue('dataField_factoryLocation') ??
            productInfo.dataField_factoryLocation
        )
      }
      if (activityType == 'consumerUse') {
        supplierLocation = JSON.parse(
          addProductForm.getFieldValue('dataField_targetMarketLocation') ??
            productInfo.dataField_targetMarketLocation
        )
      }

      if (supplierLocation?.country_code) {
        geography = supplierLocation.country_code
      }
    } catch (error) {
      console.error('Failed to parse supplier location')
    }

    if (record.emissionsFactorMatchGeography) {
      geography = record.emissionsFactorMatchGeography
    }

    let chemicalName
    if (activityType === 'material') {
      chemicalName = record.ingredient
    } else if (activityType === 'packaging') {
      chemicalName = record.material
    } else if (activityType === 'production') {
      chemicalName = record.activityName
    } else if (activityType === 'use') {
      chemicalName = record.emissionsFactorMatchActivityName ?? record.resource
    }

    setSelectedChemicalName({
      chemicalName: chemicalName,
      casNo: record.casNo ?? null,
      activityType: activityType,
      unit: record.unit,
      geography: geography,
      currentEmissionsFactor: record.emissionsFactorMatch,
      emissionsFactorMatches: record.emissionsFactorMatches ?? [],
    })

    setEmissionsFactorActivityMatchesDrawerIsOpen(true)

  }

  const handleUpdateSelectedActivityMatch = async () => {
    if (!selectedActivity.activityName) {
      return
    }

    const updateDataSource = (
      prevDataSource,
      itemKey,
      itemValue,
      setterFunction,
      uuidSetterFunction
    ) => {
      const updatedDataSource = prevDataSource.map((item) => {
        if (item[itemKey] === selectedChemicalName.chemicalName) {
          const updatedItem = {
            ...item,
            emissionsFactorMatches:
              emissionsFactorsActivityMatchesTableDataSource,
            emissionsFactorMatchGeography:
              emissionsFactorMatchForm.getFieldValue('geography'),
          }

          updatedItem.emissionsFactorMatch =
            updatedItem.emissionsFactorMatches.find(
              (activity) =>
                activity.activityName === selectedActivity.activityName &&
                activity.referenceProduct ===
                  selectedActivity.referenceProduct &&
                activity.geography === selectedActivity.geography &&
                activity.source === selectedActivity.source
            )

          if (updatedItem.emissionsFactorMatch) {
            updatedItem.emissionsFactorMatchActivityName =
              updatedItem.emissionsFactorMatch.activityName
            updatedItem.emissionsFactorMatchSource =
              updatedItem.emissionsFactorMatch.source
            updatedItem.emissionsFactorMatchGeography =
              updatedItem.emissionsFactorMatch.geography
          }

          return updatedItem
        }
        return item
      })

      setterFunction(updatedDataSource)
      uuidSetterFunction(uuidv4())
    }

    switch (selectedChemicalName.activityType) {
      case 'ingredient':
        updateDataSource(
          ingredientTableDataSource,
          'ingredient',
          selectedChemicalName.chemicalName,
          setIngredientTableDataSource,
          setAddIngredientTableKey
        )
        break
      case 'packaging':
        updateDataSource(
          packagingTableDataSource,
          'material',
          selectedChemicalName.chemicalName,
          setPackagingTableDataSource,
          setAddPackagingTableKey
        )
        break
      case 'manufacturing':
        updateDataSource(
          manufacturingMethodTableDataSource,
          'activityName',
          selectedChemicalName.chemicalName,
          setManufacturingMethodTableDataSource,
          setAddManufacturingProcessTableKey
        )
        break
      case 'consumerUse':
        updateDataSource(
          consumerUseTableDataSource,
          'resource',
          selectedChemicalName.chemicalName,
          setConsumerUseTableDataSource,
          setConsumerUseTableKey
        )
        break
    }

    setEmissionsFactorActivityMatchesDrawerIsOpen(false)
  }

  const handleEmissionsFactorUpdate = (selectedEmissionsFactor) => {
    const updateDataSource = (
      prevDataSource,
      itemKey,
      setterFunction,
      uuidSetterFunction
    ) => {
      const updatedDataSource = prevDataSource.map((item) => {
        if (item[itemKey] === selectedChemicalName.chemicalName) {
          const updatedItem = {
            ...item,
            emissionsFactorMatch: selectedEmissionsFactor,
            emissionsFactorMatchActivityName:
              selectedEmissionsFactor.activityName,
            emissionsFactorMatchSource: selectedEmissionsFactor.source,
            emissionsFactorMatchGeography: selectedEmissionsFactor.geography,
          }
          return updatedItem
        }
        return item
      })

      setterFunction(updatedDataSource)
      uuidSetterFunction(uuidv4())
    }

    switch (selectedChemicalName.activityType) {
      case 'material':
        updateDataSource(
          ingredientTableDataSource,
          'ingredient',
          setIngredientTableDataSource,
          setAddIngredientTableKey
        )
        break
      case 'packaging':
        updateDataSource(
          packagingTableDataSource,
          'material',
          setPackagingTableDataSource,
          setAddPackagingTableKey
        )
        break
      case 'production':
        updateDataSource(
          manufacturingMethodTableDataSource,
          'activityName',
          setManufacturingMethodTableDataSource,
          setAddManufacturingProcessTableKey
        )
        break
      case 'use':
        updateDataSource(
          consumerUseTableDataSource,
          'resource',
          setConsumerUseTableDataSource,
          setConsumerUseTableKey
        )
        break
    }

    setEmissionsFactorActivityMatchesDrawerIsOpen(false)
  }

  const intermediateExchangesTableColumns = [
    {
      title: 'Activity',
      dataIndex: 'exchangeName',
      key: 'exchangeName',
    },
    {
      title: 'Emissions Factor',
      dataIndex: 'activityName',
      key: 'activityName',
    },
    {
      title: 'Geography',
      dataIndex: 'geography',
      key: 'geography',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: 'Action',
      render: (record) => (
        <>
          <Button
            type="link"
            onClick={() => handleEditIntermediateExchange(record)}
          >
            <EditOutlined />
          </Button>
          <Button
            type="link"
            onClick={() => handleDeleteIntermediateExchange(record)}
          >
            <DeleteOutlined />
          </Button>
        </>
      ),
    },
  ]

  const refetchIntermediateExchanges = async (
    activityName,
    referenceProduct,
    geography,
    source
  ) => {
    const intermediateExchanges = await getEFIntermediateExchanges({
      variables: {
        activityName: activityName,
        referenceProduct: referenceProduct,
        geography: geography,
        source: source,
      },
    })

    const efIntermediateExchangesTableData =
      intermediateExchanges.data.getEFIntermediateExchanges.map((exchange) => {
        return {
          key: `${uuidv4()}-${exchange.id}`,
          id: exchange.id,
          exchangeName: exchange.exchangeName,
          activityName: exchange.exchangeEmissionsFactor.activityName,
          referenceProduct: exchange.exchangeEmissionsFactor.referenceProduct,
          geography: exchange.exchangeEmissionsFactor.geography,
          source: exchange.exchangeEmissionsFactor.source,
          unit: exchange.exchangeEmissionsFactor.unit,
          amount: exchange.amount,
          parentEmissionsFactor: exchange.parentEmissionsFactor,
        }
      })

    setIntermediateExchangesTableDataSource(efIntermediateExchangesTableData)
    setIntermediateExchangesTableKey(uuidv4())
  }

  const handleShowIntermediateExchanges = async (record) => {
    setEmissionsFactorActivityMatchesDrawerIsOpen(false)
    setEmissionsFactorIntermediateExchangesDrawerIsOpen(true)

    setSelectedParentActivity({
      activityName: record.activityName,
      referenceProduct: record.referenceProduct,
      geography: record.geography,
      source: record.source,
      unit: record.unit,
    })

    const intermediateExchanges = await getEFIntermediateExchanges({
      variables: {
        activityName: record.activityName,
        referenceProduct: record.referenceProduct,
        geography: record.geography,
        source: record.source,
      },
    })

    const efIntermediateExchangesTableData =
      intermediateExchanges.data.getEFIntermediateExchanges.map((exchange) => {
        return {
          key: `${uuidv4()}-${exchange.id}`,
          id: exchange.id,
          exchangeName: exchange.exchangeName,
          activityName: exchange.exchangeEmissionsFactor.activityName,
          referenceProduct: exchange.exchangeEmissionsFactor.referenceProduct,
          geography: exchange.exchangeEmissionsFactor.geography,
          source: exchange.exchangeEmissionsFactor.source,
          unit: exchange.exchangeEmissionsFactor.unit,
          amount: exchange.amount,
          parentEmissionsFactor: exchange.parentEmissionsFactor,
        }
      })

    setIntermediateExchangesTableDataSource(efIntermediateExchangesTableData)
    setIntermediateExchangesTableKey(uuidv4())
  }

  const handleEditIntermediateExchange = (record) => {
    addIntermediateExchangeForm.resetFields()
    setEmissionsFactorsSearchResults([])
    setEmissionsFactorSearchValue('')
    setCurrentAddExchangeStep(0)

    if (record?.activityName) {
      setSelectedExchangeActivity({
        activityName: record.activityName,
        referenceProduct: record.referenceProduct,
        geography: record.geography,
        source: record.source,
        unit: record.unit,
      })

      setEmissionsFactorsSearchResults([
        {
          id: uuidv4(),
          activityName: record.activityName,
          referenceProduct: record.referenceProduct,
          geography: record.geography,
          source: record.source,
          unit: record.unit,
        },
      ])
    }
    setEmissionsFactorsSearchResultsTableKey(uuidv4())

    addIntermediateExchangeForm.setFieldsValue({
      id: record.id,
      exchangeName: record.exchangeName,
      amount: record.amount,
      unit: record.unit,
    })

    setIsAddIntermediateExchangeModalOpen(true)
  }

  const handleDeleteIntermediateExchange = async (record) => {
    confirm({
      title: 'Do you want to delete this intermediate exchange?',
      icon: <DeleteOutlined />,
      onOk: async () => {
        const updatedDataSource = intermediateExchangesTableDataSource.filter(
          (exchange) => exchange.id !== record.id
        )

        try {
          await deleteIntermediateExchange({
            variables: {
              exchangeId: record.id,
            },
          })

          setIntermediateExchangesTableDataSource(updatedDataSource)
          setIntermediateExchangesTableKey(uuidv4())

          message.success('Intermediate exchange deleted successfully')
          return true
        } catch (error) {
          message.error('Failed to delete intermediate exchange')
          return false
        }
      },
      onCancel() {
        return false
      },
    })
  }

  const handlePackagingSupplierChange = (values: any) => {
    if (values.supplierOrigin) {
      values.predictSourceIsLoading = false
      values.predictEmissionsFactorMatchIsLoading = true
      handlePredictPackagingEmissionsFactors(values)
      return values
    }

    values.predictSourceIsLoading = true
    values.predictEmissionsFactorMatchIsLoading = false
    handlePredictPackagingSource(values)
    return values
  }

  const confirmAddPackaging = (values) => {
    if (values?.key?.length ?? 0) {
      const updatedDataSource = packagingTableDataSource.map((packaging) => {
        if (packaging.key === values.key) {
          if (
            packaging.material.trim().toLowerCase() !==
            values.material.trim().toLowerCase()
          ) {
            values = handlePackagingSupplierChange(values)
          }
          return { ...packaging, ...values }
        }
        return packaging
      })
      setPackagingTableDataSource(updatedDataSource)
    } else {
      values.key = uuidv4()
      values = handlePackagingSupplierChange(values)
      setPackagingTableDataSource((prevDataSource) => [
        ...prevDataSource,
        values,
      ])
    }

    setAdvancedAddPackagingFieldsIsChecked(false)
    addPackagingForm.resetFields()
    setIsAddPackagingModalOpen(false)
    setAddPackagingTableKey(uuidv4())
  }

  const handleEditPackaging = (record) => {
    setAdvancedAddPackagingFieldsIsChecked(false)
    addPackagingForm.resetFields()
    addPackagingForm.setFieldsValue(record)
    if (record['dataField_advancedFields']) {
      setAdvancedAddPackagingFieldsIsChecked(true)
    }
    setIsAddPackagingModalOpen(true)
  }

  const handleDeletePackaging = (key) => {
    confirm({
      title: 'Do you want to delete this packaging?',
      icon: <DeleteOutlined />,
      okButtonProps: { className: 'confirm-delete-packaging-button' },
      onOk() {
        const updatedTableData = packagingTableDataSource.filter(
          (packaging) => packaging.key !== key
        )
        setPackagingTableDataSource(updatedTableData)
        setAddPackagingTableKey(uuidv4())
      },
      onCancel() {
        return false
      },
    })
  }

  const handleDeleteManufacturingMethod = (key) => {
    confirm({
      title: 'Do you want to delete this manufacturing method?',
      icon: <DeleteOutlined />,
      okButtonProps: {
        className: 'confirm-delete-manufacturing-method-button',
      },
      onOk() {
        const updatedTableData = manufacturingMethodTableDataSource.filter(
          (manufacturingMethod) => manufacturingMethod.key !== key
        )
        setManufacturingMethodTableDataSource(updatedTableData)
        setAddManufacturingProcessTableKey(uuidv4())
      },
      onCancel() {
        return false
      },
    })
  }

  const handleDeleteTransportSegment = (key) => {
    confirm({
      title: 'Do you want to delete this transport segment?',
      icon: <DeleteOutlined />,
      okButtonProps: {
        className: 'confirm-delete-transport-segment-button',
      },
      onOk() {
        const updatedTableData = transportationTableDataSource.filter(
          (transportSegment) => transportSegment.key !== key
        )
        setTransportationTableDataSource(updatedTableData)
        setAddTransportationTableKey(uuidv4())
      },
      onCancel() {
        return false
      },
    })
  }

  const productInfoCollapseItems: CollapseProps['items'] = [
    {
      key: '1',
      label: 'Optional Data',
      children: (
        <>
          <Form.Item
            label="Brand"
            name="brand"
            tooltip="Brand name of the product"
          >
            <Input placeholder="Ariel" />
          </Form.Item>
          <Form.Item
            label="Product ID/Code"
            name="sku"
            tooltip="SKU or UPC Code"
          >
            <Input placeholder="#123456" />
          </Form.Item>
          <Form.Item
            name="annualSalesVolume"
            label="Annual Sales Volume"
            tooltip="Enter the product sales volume in units"
          >
            <InputNumber
              style={{ float: 'left', width: '50%' }}
              min="1"
              placeholder="25000"
              addonAfter={<TagsOutlined />}
            />
          </Form.Item>

          <Form.Item
            name="functionalUnit"
            label="Functional Unit"
            tooltip="A unit of product, used to compare environmental impacts. Example, 10 ml of shampoo."
          >
            <Input
              min="0"
              style={{ float: 'left', width: '50%' }}
              placeholder="1.5L"
            />
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="Product Image URL"
            tooltip="Enter the link to your product image"
          >
            <Input
              placeholder="Product Image URL"
              addonBefore={<PictureOutlined />}
            />
          </Form.Item>
        </>
      ),
    },
  ]

  const transportationCollapseItems: CollapseProps['items'] = [
    {
      key: '1',
      label: (
        <p style={{ fontSize: '16px', fontWeight: 'bold' }}>Ingredients</p>
      ),
      children: <></>,
    },
    {
      key: '2',
      label: <p style={{ fontSize: '16px', fontWeight: 'bold' }}>Packaging</p>,
      children: <></>,
    },
    {
      key: '3',
      label: (
        <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
          Factory To Retail
        </p>
      ),
      children: <></>,
    },
    {
      key: '3',
      label: (
        <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
          Retail To Customer
        </p>
      ),
      children: <></>,
    },
  ]

  const formItemLayout = {
    labelCol: {
      xs: { span: 8 },
      md: { span: 8 },
      lg: { span: 8 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 8 },
      sm: { span: 8 },
      md: { span: 8 },
      lg: { span: 8 },
    },
  }

  const handleCollapseChange = (key) => {
    setAddProductOptioinalDataCollapseKey(key)
  }

  const handleFactoryLocationChange = async () => {
    setIngredientTableDataSource((prevDataSource) =>
      prevDataSource.map((_ingredient) => {
        _ingredient.predictSourceIsLoading = true
        _ingredient.predictEmissionsFactorMatchIsLoading = false
        handlePredictIngredientSource(
          _ingredient,
          addProductForm.getFieldValue('category')
        )
        return _ingredient
      })
    )
    setAddIngredientTableKey(uuidv4())

    setPackagingTableDataSource((prevDataSource) =>
      prevDataSource.map((_packaging) => {
        _packaging.predictSourceIsLoading = true
        handlePredictPackagingSource(_packaging)
        return _packaging
      })
    )
    setAddPackagingTableKey(uuidv4())

    setTransportationTableDataSource((prevDataSource) =>
      prevDataSource.map((_transportSegment) => {
        if (_transportSegment.segmentName === 'Source to Factory') {
          _transportSegment.destination = addProductForm.getFieldValue(
            'factoryLocation'
          )
          _transportSegment.dataField_destination = addProductForm.getFieldValue(
            'dataField_factoryLocation'
          )
        }
        return _transportSegment
      })
    )
    setAddTransportationTableKey(uuidv4())
  }

  const handleTargetMarketLocationChange = async () => {

    setTransportationTableDataSource((prevDataSource) =>
      prevDataSource.map((_transportSegment) => {
        if (_transportSegment.segmentName === 'Factory to Retail') {
          _transportSegment.destination = addProductForm.getFieldValue(
            'targetMarketLocation'
          )
          _transportSegment.dataField_destination = addProductForm.getFieldValue(
            'dataField_targetMarketLocation'
          )
        }
        return _transportSegment
      })
    )
    setAddTransportationTableKey(uuidv4())

  }

  const addProductContent = (
    <>
      <p style={{ textAlign: 'left', fontSize: '16px' }}>
        Let’s enter some basic information on your product
      </p>
      <Form
        style={{ marginTop: '20px' }}
        form={addProductForm}
        {...formItemLayout}
        initialValues={{
          weightUnit: 'g',
        }}
      >
        <Form.Item
          label="Product Name"
          id="productName"
          name="productName"
          style={{ textAlign: 'left' }}
          rules={[{ required: true, message: 'Product name is required' }]}
          tooltip="Name of the product"
        >
          <Input placeholder="Ariel Laundry Liquid Detergent, 1L" />
        </Form.Item>

        <Button
          icon={<CopilotLogo />}
          id="predictProductCategoryButton"
          onClick={handleProductNameChange}
          style={{ position: 'absolute', marginLeft: '15%' }}
          type="dashed"
          className="copilot-button"
          loading={predictedProductCategoryLoading}
        >
          Use Copilot
        </Button>

        <Form.Item
          label="Product Category"
          id="category"
          name="category"
          style={{ textAlign: 'left' }}
          rules={[{ required: true, message: 'Product category is required' }]}
          tooltip="Select a category from the list or click on the Use Copilot button to predict the category for you"
        >
          <Select
            showSearch
            allowClear
            options={productCategories}
            onSelect={() => {
              setPredictedFormValues((prevValues) => {
                return { ...prevValues, category: false }
              })
            }}
            suffixIcon={null}
            style={{ textAlign: 'left' }}
            placeholder="Laundry Liquid"
            filterOption={(inputValue, option) =>
              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
          />
        </Form.Item>

        <Form.Item name="dataField_factoryLocation" hidden>
          <Input />
        </Form.Item>

        <Form.Item name="tags" hidden>
          <Input />
        </Form.Item>

        {!basicProductEditing ? (
          <Form.Item
          label="Finished Goods Factory Location"
          name="factoryLocation"
          style={{ textAlign: 'left' }}
          rules={[{ required: true, message: 'factory location is required' }]}
          tooltip="The location of the factory"
        >
          <Select
            showSearch
            allowClear
            clearIcon={<CloseCircleFilled id="factory-location-clear-icon" />}
            suffixIcon={null}
            style={{ width: '100%', textAlign: 'left' }}
            options={mapboxAutocompleteOptions}
            placeholder="London, United Kingdom"
            onSearch={handleFactoryLocationSearch}
            onSelect={(value, option) => {
              addProductForm.setFieldValue(
                'dataField_factoryLocation',
                option.data ?? null
              )
              handleFactoryLocationChange()
            }}
            onBlur={() => {
              setMapboxAutocompleteOptions([])
            }}
            notFoundContent={
              productInfoFactoryLocationMapboxDataLoading ? (
                <Spin size="small" />
              ) : null
            }
          />
        </Form.Item>
        ) : null}

        <Form.Item name="dataField_targetMarketLocation" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          label="Target Market/Region"
          name="targetMarketLocation"
          style={{ textAlign: 'left' }}
          rules={[
            { required: true, message: 'target market/region is required' },
          ]}
          tooltip="Country/Region where the product is being sold"
        >
          <Select
            showSearch
            suffixIcon={null}
            allowClear
            clearIcon={<CloseCircleFilled id="target-market-location-clear-icon" />}
            style={{ width: '100%', textAlign: 'left' }}
            options={mapboxAutocompleteOptions}
            placeholder="United States"
            onSearch={handleTargetMarketLocationSearch}
            onSelect={(value, option) => {
              addProductForm.setFieldValue(
                'dataField_targetMarketLocation',
                option.data ?? null
              )
              handleTargetMarketLocationChange()
            }}
            onBlur={() => {
              setMapboxAutocompleteOptions([])
            }}
            notFoundContent={
              productInfoFactoryLocationMapboxDataLoading ? (
                <Spin size="small" />
              ) : null
            }
          />
        </Form.Item>

        <Collapse
          style={{ textAlign: 'left' }}
          ghost
          onChange={handleCollapseChange}
          activeKey={addProductOptioinalDataCollapseKey}
          items={productInfoCollapseItems}
        />
      </Form>
    </>
  )

  const componentTableColumns = [
    {
      title: 'Component Id',
      dataIndex: 'componentId',
      sorter: true,
    },
    {
      title: 'Component Name',
      dataIndex: 'component',
      sorter: true,
    },
  ]

  const ingredientTableColumns = [
    {
      title: 'Material',
      dataIndex: 'ingredient',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'CAS No.',
      dataIndex: 'casNo',
      sorter: true,
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      render: (text, record) =>
        `${(!calculateEmissionsPerUnit ? formatFloat(Number(text) * (productInfo.annualSalesVolume ??  1)) : formatFloat(text ?? 0, 2))} ${record.weightUnit}`,
      sorter: true,
    },
    {
      title: 'Supplier Origin',
      dataIndex: 'supplierOrigin',
      render: (text, record) =>
        record.predictSourceIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          record.dataField_supplierLocation?.includes('predicted') ? (
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
          ) : (
            <>{text}</>
          )
        ) : (
          'N/A'
        ),
      sorter: true,
    },
    {
      title: 'Matched Activity',
      dataIndex: 'emissionsFactorMatchActivityName',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <>
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
            <Button
              icon={<EditOutlined />}
              type="link"
              onClick={() =>
                handleShowEmissionsFactorsActivityRecommendations(
                  record,
                  'material'
                )
              }
            />
          </>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Source',
      dataIndex: 'emissionsFactorMatchSource',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <Tag color="green">{text}</Tag>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Action',
      fixed: 'right',
      render: (key, record) => (
        <>
          <Button type="link" onClick={() => handleEditIngredient(record)}>
            Edit
          </Button>
          <Button
            type="link"
            onClick={() => handleDeleteIngredient(record.key)}
          >
            Delete
          </Button>
        </>
      ),
      width: 50,
    },
  ]

  const packagingTableColumns = [
    {
      title: 'Material',
      dataIndex: 'material',
      sorter: true,
      fixed: 'left',
      render: (text, record) => (
        <>
          {text}
          {record.predicted && (
            <>
              <Divider type="vertical" />
              <PredictedIcon />
            </>
          )}
        </>
      ),
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      render: (text, record) =>
        `${(!calculateEmissionsPerUnit ? formatFloat(Number(text) * (productInfo.annualSalesVolume ??  1)) : formatFloat(text ?? 0, 2))} ${record.weightUnit}`,
      sorter: true,
    },
    {
      title: 'Tier',
      dataIndex: 'tier',
      sorter: true,
    },
    {
      title: 'Supplier Origin',
      dataIndex: 'supplierOrigin',
      render: (text, record) =>
        record.predictSourceIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          record.dataField_supplierLocation?.includes('predicted') ? (
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
          ) : (
            <>{text}</>
          )
        ) : (
          'N/A'
        ),
      sorter: true,
    },
    {
      title: 'Matched Activity',
      dataIndex: 'emissionsFactorMatchActivityName',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <>
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
            <Button
              icon={<EditOutlined />}
              type="link"
              onClick={() =>
                handleShowEmissionsFactorsActivityRecommendations(
                  record,
                  'packaging'
                )
              }
            />
          </>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Source',
      dataIndex: 'emissionsFactorMatchSource',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <Tag color="green">{text}</Tag>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Action',
      fixed: 'right',
      render: (key, record) => (
        <>
          <Button type="link" onClick={() => handleEditPackaging(record)}>
            Edit
          </Button>
          <Button
            type="link"
            className="delete-packaging-button"
            onClick={() => handleDeletePackaging(record.key)}
          >
            Delete
          </Button>
        </>
      ),
      width: 50,
    },
  ]

  const manufacturingTableColumns = [
    {
      title: 'Production Activity',
      dataIndex: 'activityName',
      sorter: true,
      render: (text, record) =>
        text === preferredManufacturingActivityName ? (
          <>
            {text}
            <Divider type="vertical" />
            <PredictedIcon />
          </>
        ) : (
          text
        ),
    },
    {
      title: 'Matched Activity',
      dataIndex: 'emissionsFactorMatchActivityName',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <>
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
            <Button
              icon={<EditOutlined />}
              type="link"
              onClick={() =>
                handleShowEmissionsFactorsActivityRecommendations(
                  record,
                  'production'
                )
              }
            />
          </>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Activity Type',
      dataIndex: 'activityType',
      sorter: true,
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      render: (text, record) =>
        !record.amount ? (
          <>
            Predicted
            <Divider type="vertical" />
            <PredictedIcon />
          </>
        ) : (
          text
        ),
      sorter: true,
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      render: (text, record) => {
        return manufacturingMethodActivityUnitMapping[record.activityType]
      },
      sorter: true,
    },
    {
      title: 'Action',
      render: (key, record) => (
        <>
          <Button
            type="link"
            onClick={() => handleEditManufacturingMethod(record)}
          >
            Edit
          </Button>
          <Button
            type="link"
            onClick={() => handleDeleteManufacturingMethod(record.key)}
          >
            Delete
          </Button>
        </>
      ),
      width: 50,
    },
  ]

  const transportationTableColumns = [
    {
      title: 'Transportation Step',
      dataIndex: 'segmentName',
      sorter: true,
      render: (text, record) =>
        preferredTransportSegments.map((x) => x.segmentName).includes(text) ? (
          <>
            {text}
            <Divider type="vertical" />
            <PredictedIcon />
          </>
        ) : (
          text
        ),
    },
    {
      title: 'Destination',
      dataIndex: 'destination',
      sorter: true,
      render: (text, record) => `${text} (Average)`,
    },
    {
      title: 'Action',
      render: (key, record) => (
        <>
          <Button
            type="link"
            onClick={() => handleEditTransportSegment(record)}
          >
            Edit
          </Button>
          <Button
            type="link"
            onClick={() => handleDeleteTransportSegment(record.key)}
          >
            Delete
          </Button>
        </>
      ),
      width: 50,
    },
  ]

  const consumerUseTableColumns = [
    {
      title: 'Resource',
      dataIndex: 'resource',
      sorter: true,
    },
    {
      title: 'Matched Activity',
      dataIndex: 'emissionsFactorMatchActivityName',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <>
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
            <Button
              icon={<EditOutlined />}
              type="link"
              onClick={() =>
                handleShowEmissionsFactorsActivityRecommendations(
                  record,
                  'use'
                )
              }
            />
          </>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      render: (text, record) =>
        !calculateEmissionsPerUnit ? formatFloat(Number(text) * (productInfo.annualSalesVolume ??  1)) : formatFloat(text ?? 0, 2),
      sorter: true,
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      sorter: true,
    },
  ]

  const reviewIngredientTableColumns = [
    {
      title: 'Material',
      dataIndex: 'ingredient',
      sorter: true,
    },
    {
      title: 'CAS No.',
      dataIndex: 'casNo',
      sorter: true,
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      render: (text, record) =>
        `${(!calculateEmissionsPerUnit ? formatFloat(Number(text) * (productInfo.annualSalesVolume ??  1)) : formatFloat(text ?? 0, 2))} ${record.weightUnit}`,
      sorter: true,
    },
    {
      title: 'Supplier Origin',
      dataIndex: 'supplierOrigin',
      render: (text, record) =>
        record.predictSourceIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          record.dataField_supplierLocation?.includes('predicted') ? (
            <>
              {text}
              <Divider type="vertical" />
              <PredictedIcon />
            </>
          ) : (
            <>{text}</>
          )
        ) : (
          'N/A'
        ),
      sorter: true,
    },
    {
      title: 'Matched Activity',
      dataIndex: 'emissionsFactorMatchActivityName',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <>
            {text}
            <Divider type="vertical" />
            <PredictedIcon />
          </>
        ) : (
          'N/A'
        ),
    },
    {
      title: 'Source',
      dataIndex: 'emissionsFactorMatchSource',
      sorter: true,
      render: (text, record) =>
        record.predictEmissionsFactorMatchIsLoading ? (
          <PredictionLoadingIcon />
        ) : text ? (
          <Tag color="green">{text}</Tag>
        ) : (
          'N/A'
        ),
    },
  ]

  const emissionsFactorsActivityMatchesTableColumns: TableColumnsType = [
    {
      title: 'Select',
      render: (key, record) => (
        <Radio
          value={`${record['activityName']}/${record['referenceProduct']}/${record['geography']}/${record['source']}`}
          checked={
            selectedActivity.activityName === record['activityName'] &&
            selectedActivity.referenceProduct === record['referenceProduct'] &&
            selectedActivity.geography === record['geography'] &&
            selectedActivity.source === record['source']
          }
          onChange={() =>
            setSelectedActivity({
              activityName: record['activityName'],
              referenceProduct: record['referenceProduct'],
              geography: record['geography'],
              source: record['source'],
              unit: record['unit'],
            })
          }
        />
      ),
    },
    {
      title: 'Activity Name',
      dataIndex: 'activityName',
      render: (text, record) =>
        record.curated ? (
          <>
            {text}
            <Divider type="vertical" />
            <Tag color="green">Preferred</Tag>
          </>
        ) : (
          <>{text}</>
        ),
    },
    {
      title: 'Geography',
      dataIndex: 'geography',
    },
    {
      title: 'Description',
      dataIndex: 'productInformation',
    },
    {
      title: 'Confidence',
      dataIndex: 'confidence',
      render: (text, record) =>
        text?.length ? (
          <>
            <Tag color="green">{text.toUpperCase()}</Tag>
          </>
        ) : (
          <>N/A</>
        ),
    },
    {
      title: 'Explanation',
      dataIndex: 'explanation',
      render: (text, record) => (text?.length ? <>{text}</> : <>N/A</>),
    },
    {
      title: 'Action',
      render: (key, record) => (
        <>
          <Button
            type="link"
            onClick={() => handleShowIntermediateExchanges(record)}
          >
            <EditOutlined />
          </Button>
        </>
      ),
      width: 50,
    },
  ]

  const addIngredientContent = (
    <>
      <p style={{ textAlign: 'left', fontSize: '16px' }}>
        Let’s enter the raw materials including packaging for your product
        <Tooltip title="The Material Stage consists of acquisition and processing of resources extracted from nature into the material inputs used in production and packaging.">
          <Button
            icon={<QuestionCircleOutlined />}
            type="link"
            style={{ color: 'grey' }}
          ></Button>
        </Tooltip>
      </p>

      <Button
        style={{
          marginTop: '20px',
          backgroundColor: '#f3c314d4',
          float: 'left',
        }}
        onClick={() => {
          setAdvancedAddIngredientFieldsIsChecked(false)
          addIngredientForm.resetFields()
          setIsAddIngredientModalOpen(true)
        }}
        id="add-ingredient-button"
      >
        <Space>
          <PlusCircleOutlined />
          Add Raw Material
        </Space>
      </Button>
      <Modal
        title="Add Raw Material"
        open={isAddIngredientModalOpen}
        onCancel={() => setIsAddIngredientModalOpen(false)}
        onOk={() => addIngredientForm.submit()}
        okText="Add"
        okButtonProps={{
          id: 'add-ingredient-submit-button',
          style: { backgroundColor: '#f3c314d4' },
        }}
      >
        <Form
          form={addIngredientForm}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onFinish={confirmAddIngredient}
          initialValues={{ weightUnit: 'g', Wastage: 3 }}
          id="add-ingredient-form"
        >
          <Form.Item style={{ display: 'none' }} name="key">
            <Input type="hidden" />
          </Form.Item>
          <Form.Item
            label="Raw Material Name"
            name="ingredient"
            rules={[
              { required: true, message: 'Raw Material name is required' },
            ]}
            tooltip="Specify the raw material name or choose one from the list"
          >
            <AutoComplete
              style={{ float: 'left' }}
              allowClear
              options={rawMaterialsIngredient}
              placeholder="Citric Acid"
              onSelect={(value, option) => {
                addIngredientForm.setFieldValue('casNo', option.casNumber)
              }}
              filterOption={(inputValue, option) =>
                option!.value
                  .toUpperCase()
                  .indexOf(inputValue.toUpperCase()) !== -1
              }
            />
          </Form.Item>

          <Row gutter={16}>
            <Col flex="1">
              <Form.Item
                label="Weight"
                name="weight"
                rules={[{ required: true, message: 'weight is required' }]}
                tooltip="Specify the weight with units"
              >
                <InputNumber
                  min="0"
                  style={{ width: '100%' }}
                  placeholder="120"
                />
              </Form.Item>
            </Col>
            <Col flex="1">
              <Form.Item
                name="weightUnit"
                label="Unit"
                rules={[{ required: true, message: 'unit is required' }]}
              >
                <Select
                  style={{ width: '100%' }}
                  options={[
                    {
                      label: 'Grams (g)',
                      value: 'g',
                    },
                    {
                      label: 'Kilograms (Kg)',
                      value: 'kg',
                    },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col flex="1">
            <Form.Item
              label="Component"
              name="component"
              tooltip="component name"
            >
              <Input
                style={{ float: 'left' }}
                placeholder="Bottle"
              />
            </Form.Item>
            <Form.Item
              label="Description"
              name="description"
              tooltip="Description of the raw material"
            >
              <Input />
            </Form.Item>
            </Col>
          </Row>

          <Form.Item name="dataField_advancedFields" valuePropName="checked">
            <Checkbox
              id="advanced-ingredient-fields-checkbox"
              onChange={(e) =>
                setAdvancedAddIngredientFieldsIsChecked(e.target.checked)
              }
              checked={advancedAddIngredientFieldsIsChecked}
              style={{ float: 'left' }}
            >
              Advanced Fields
            </Checkbox>
          </Form.Item>

          <Form.Item
            label="Proprietary/internal name"
            name="proprietaryName"
            tooltip="Specify the proprietary/internal name of the ingredient"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <Input placeholder="Citracid-50" />
          </Form.Item>

          <Form.Item
            label="CAS Number"
            name="casNo"
            tooltip="CAS Number of the ingredient"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <Input
              style={{ float: 'left' }}
              placeholder="77-92-9"
              addonAfter={<NumberOutlined />}
            />
          </Form.Item>

          <Form.Item
            label="Supplier Name"
            name="supplierName"
            tooltip="Specify the supplier name"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <Input style={{ width: '100%' }} placeholder="Univar" />
          </Form.Item>

          <Form.Item name="dataField_supplierLocation" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="supplierOrigin"
            label="Supplier Origin"
            tooltip="Specify the supplier location"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <Select
              showSearch
              suffixIcon={null}
              allowClear
              style={{ width: '100%', float: 'left' }}
              options={mapboxAutocompleteOptions}
              placeholder="London, United Kingdom"
              onSearch={handleIngredientSuppierLocationSearch}
              onSelect={(value, option) => {
                addIngredientForm.setFieldValue(
                  'dataField_supplierLocation',
                  option.data ?? null
                )
              }}
              notFoundContent={
                ingredientSupplierLocationMapboxDataLoading ? (
                  <Spin size="small" />
                ) : null
              }
              onBlur={() => {
                setMapboxAutocompleteOptions([])
              }}
            />
          </Form.Item>
          <Form.Item
            name="recycledContent"
            label="Recycled Content"
            tooltip="Enter recycled content of the material value in percentage"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <InputNumber
              min="0"
              max="100"
              placeholder="85"
              addonBefore={<PercentageOutlined />}
            />
          </Form.Item>
          <Form.Item
            name="wastage"
            label="Wastage"
            tooltip="Enter ingredient wastage value in percentage"
            hidden={!advancedAddIngredientFieldsIsChecked}
          >
            <InputNumber
              min="0"
              style={{ float: 'left' }}
              placeholder="3"
              addonBefore={<PercentageOutlined />}
            />
          </Form.Item>
        </Form>
      </Modal>
      <DataTable
        id={'add-ingredient-table'}
        key={addIngredientTableKey}
        style={{
          paddingTop: '20px',
          paddingRight: '45px',
        }}
        paginate={false}
        bordered
        columns={ingredientTableColumns}
        data={ingredientTableDataSource}
      />
    </>
  )

  const handleManufacturingActivityChange = () => {
    const activityName =
      addManufacturingMethodForm.getFieldValue('activityName')
    const activityType =
      addManufacturingMethodForm.getFieldValue('activityType')

    if (activityType) setSelectedManufacturingActivity(activityType)

    const activityInfo = manufacturingActivityName.find(
      (x) => x.value === activityName
    )

    if (!activityInfo || activityType !== 'Total Carbon Emissions') {
      return
    }

    addManufacturingMethodForm.setFieldsValue({
      amount: activityInfo.co2eKg,
    })
  }

  const addManufacturingContent = (
    <>
      <p style={{ textAlign: 'left', fontSize: '16px' }}>
        Let's enter the manufacturing emissions for your product
        <Tooltip title="The Manufacturing Stage comprises of the actual production processes within the facility where the purchased raw materials are transformed into your end product.">
          <Button
            icon={<QuestionCircleOutlined />}
            type="link"
            style={{ color: 'grey' }}
          ></Button>
        </Tooltip>
      </p>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Scrap Rate (%)"
            name="scrapRate"
            tooltip="Enter the percentage of material that is scrapped during manufacturing."
            rules={[{ required: true, message: 'Scrap rate is required' }]}
          >
            <InputNumber min="0" max="100" placeholder="Enter scrap rate" style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Scrap Fate"
            name="scrapFate"
            tooltip="Select the fate of the scrapped material."
            rules={[{ required: true, message: 'Scrap fate is required' }]}
          >
            <Select
              options={[
                { label: 'Landfill', value: 'landfill' },
                { label: 'Recycling', value: 'recycling' },
                { label: 'Incineration', value: 'incineration' },
                { label: 'Composting', value: 'composting' },
              ]}
              placeholder="Select scrap fate"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Button
        style={{
          marginTop: '20px',
          backgroundColor: '#f3c314d4',
          float: 'left',
        }}
        onClick={() => {
          addManufacturingMethodForm.resetFields()
          setCustomManufacturingEF(false)
          setIsAddManufacturingMethodModalOpen(true)
        }}
        id="add-ingredient-button"
      >
        <Space>
          <PlusCircleOutlined />
          Select Manufacturing Method
        </Space>
      </Button>
      <Button
        icon={<CopilotLogo />}
        style={{ float: 'left', marginTop: '20px', marginLeft: 10 }}
        loading={getManufacturingMethodsIsLoading}
        onClick={fetchManufacturingMethods}
        type="dashed"
        className="copilot-button"
      >
        Use Copilot
      </Button>
      <Modal
        title="Manufacturing Method"
        open={isAddManufacturingMethodModalOpen}
        onCancel={() => setIsAddManufacturingMethodModalOpen(false)}
        onOk={() => addManufacturingMethodForm.submit()}
        okText="Add"
        okButtonProps={{
          id: 'add-manufacturing-method-submit-button',
          style: { backgroundColor: '#f3c314d4' },
        }}
      >
        <Form
          form={addManufacturingMethodForm}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onFinish={confirmAddManufacturing}
          id="add-manufacturing-method-form"
          initialValues={{
            activityType: 'Total Carbon Emissions',
          }}
        >
          <Form.Item style={{ display: 'none' }} name="key">
            <Input type="hidden" />
          </Form.Item>
          <Form.Item
            label="Activity Name"
            name="activityName"
            rules={[{ required: true, message: 'Activity name is required' }]}
            tooltip="Specify the activity name"
          >
            <AutoComplete
              style={{ float: 'left' }}
              allowClear
              options={manufacturingActivityName}
              placeholder="Production Process"
              filterOption={(inputValue, option) =>
                option!.value
                  .toUpperCase()
                  .indexOf(inputValue.toUpperCase()) !== -1
              }
              onChange={handleManufacturingActivityChange}
            />
          </Form.Item>
          <Form.Item name="activityType" label="Activity Type / Unit" required>
            <Select
              style={{ width: '100%' }}
              options={[
                {
                  label: 'Total Carbon Emissions (Kg CO2e/Kg)',
                  value: 'Total Carbon Emissions',
                },
                {
                  label: 'Electricity usage (KWh/Kg)',
                  value: 'Electricity usage',
                },
                {
                  label: 'Water (Liters/Kg)',
                  value: 'Water',
                },
              ]}
              onChange={handleManufacturingActivityChange}
            />
          </Form.Item>
          {selectedManufacturingActivity === 'Total Carbon Emissions' && (
            <Form.Item
              name="customManufacturingEF"
              tooltip="Specify custom EF value from primary data"
              required
            >
              <Tooltip title="Specify custom EF value from primary data">
                <Switch
                  checked={customManufacturingEF}
                  onChange={(checked) => {
                    setCustomManufacturingEF(checked)
                  }}
                />
                <p style={{ position: 'absolute', top: 5, left: 55 }}>
                  Custom EF
                </p>
              </Tooltip>
            </Form.Item>
          )}

          {selectedManufacturingActivity === 'Total Carbon Emissions' ? (
            customManufacturingEF ? (
              <Form.Item
                label="Custom Emissions Factor (Kg CO2e)"
                name="amount"
                rules={[
                  {
                    required: true,
                    message: 'Custom Emissions Factor is required',
                  },
                ]}
                tooltip="Enter the custom emissions factor as part of your primary data"
              >
                <InputNumber style={{ width: '100%' }} placeholder="0.0123" />
              </Form.Item>
            ) : null
          ) : (
            <Form.Item
              label="Amount"
              name="amount"
              rules={[
                {
                  required: true,
                  message: 'amount is required',
                },
              ]}
              tooltip="Specify the amount or resource used in the manufacturing process."
            >
              <InputNumber style={{ width: '100%' }} placeholder="120" />
            </Form.Item>
          )}
        </Form>
      </Modal>
      <DataTable
        id={'add-manufacturing-table'}
        key={addManufacturingProcessTableKey}
        style={{
          paddingTop: '20px',
          paddingRight: '45px',
        }}
        paginate={false}
        bordered
        columns={manufacturingTableColumns}
        data={manufacturingMethodTableDataSource}
      />
    </>
  )

  const handleTransportSegmentSelectChange = (value) => {
    if (value === 'Retail to Customer (ecommerce)') {
      return setTransportSegmentTooltipMessage(
        'For non ecommerce scenarios, you can remove this to assume consumer transport is part of regular shopping trip not attributed specifically to this product'
      )
    }

    setTransportSegmentTooltipMessage(
      'Specify the name of the transportation step/segment'
    )
  }

  const addTransportationContent = (
    <>
      <p style={{ textAlign: 'left', marginRight: '20px', fontSize: '16px' }}>
        Let’s enter the transportation routes for your product.
        <Tooltip title="The Transportation Stage starts when the raw materials are transported from their source and end when when the consumer takes possession of the finished product. Several legs of distribution may occur for one product.">
          <Button
            icon={<QuestionCircleOutlined />}
            type="link"
            style={{ color: 'grey' }}
          ></Button>
        </Tooltip>
        <br />
        <br />
        Add a “Retail to customer” segment if the product is being sold via
        ecommerce. For retail via stores, the transport emissions can be
        considered as part of normal shopping trip and excluded from the
        assessment.
      </p>

      <Button
        style={{
          marginTop: '20px',
          backgroundColor: '#f3c314d4',
          float: 'left',
        }}
        onClick={() => {
          addTransportationForm.resetFields()
          setIsAddTransportationModalOpen(true)
        }}
        id="add-ingredient-button"
      >
        <Space>
          <PlusCircleOutlined />
          Add Transportation Step
        </Space>
      </Button>
      <Button
        icon={<CopilotLogo />}
        style={{ float: 'left', marginTop: '20px', marginLeft: 10 }}
        onClick={fetchTransportSegments}
        type="dashed"
        className="copilot-button"
      >
        Use Copilot
      </Button>
      <Modal
        title="Add Transportation Step"
        open={isAddTransportationModalOpen}
        onCancel={() => setIsAddTransportationModalOpen(false)}
        onOk={() => addTransportationForm.submit()}
        okText="Add"
        okButtonProps={{
          id: 'add-transportation-submit-button',
          style: { backgroundColor: '#f3c314d4' },
        }}
      >
        <Form
          form={addTransportationForm}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onFinish={confirmAddTransportSegment}
          id="add-manufacturing-method-form"
          initialValues={{
            activityType: 'Total Carbon Emissions',
          }}
        >
          <Form.Item style={{ display: 'none' }} name="key">
            <Input type="hidden" />
          </Form.Item>
          <Form.Item
            label="Transportation Segment"
            name="segmentName"
            rules={[
              {
                required: true,
                message: 'Transportation step/segment name is required',
              },
            ]}
            tooltip={transportSegmentTooltipMessage}
          >
            <Select
              style={{ float: 'left' }}
              allowClear
              options={TransportSegment}
              placeholder="Factory to Retail"
              onChange={handleTransportSegmentSelectChange}
              filterOption={(inputValue, option) =>
                option!.value
                  .toUpperCase()
                  .indexOf(inputValue.toUpperCase()) !== -1
              }
            />
          </Form.Item>
          <Form.Item name="dataField_destination" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="destination"
            label="Destination"
            tooltip="Specify the destination"
          >
            <Select
              showSearch
              suffixIcon={null}
              allowClear
              style={{ width: '100%', float: 'left' }}
              options={mapboxAutocompleteOptions}
              placeholder="United Kingdom"
              onSearch={handleTransportSegmentDestinationSearch}
              onSelect={(value, option) => {
                addTransportationForm.setFieldValue(
                  'dataField_destination',
                  option.data ?? null
                )
              }}
              notFoundContent={
                transportSegmentDestinationMapboxDataLoading ? (
                  <Spin size="small" />
                ) : null
              }
              onBlur={() => {
                setMapboxAutocompleteOptions([])
              }}
            />
          </Form.Item>
        </Form>
      </Modal>
      <DataTable
        id={'add-transportation-table'}
        key={addTransportationTableKey}
        style={{
          paddingTop: '20px',
          paddingRight: '45px',
        }}
        paginate={false}
        bordered
        columns={transportationTableColumns}
        data={transportationTableDataSource}
      />
    </>
  )

  const addPackagingContent = (
    <>
      <Button
        style={{
          backgroundColor: '#f3c314d4',
          float: 'left',
        }}
        onClick={() => {
          setAdvancedAddPackagingFieldsIsChecked(false)
          addPackagingForm.resetFields()
          setIsAddPackagingModalOpen(true)
        }}
        id="add-packaging-button"
      >
        <Space>
          <PlusCircleOutlined />
          Add Packaging
        </Space>
      </Button>
      <Button
        disabled={ingredientTableDataSource.length === 0}
        onClick={populatePredictedPackagingMaterials}
        loading={predictedProductPackagingIsLoading}
        icon={<CopilotLogo />}
        style={{ float: 'left', marginLeft: 10 }}
        type="dashed"
        className="copilot-button"
      >
        Use Copilot
      </Button>
      <Modal
        title="Add Packaging"
        open={isAddPackagingModalOpen}
        onCancel={() => setIsAddPackagingModalOpen(false)}
        onOk={() => addPackagingForm.submit()}
        okText="Add"
        okButtonProps={{
          id: 'add-packaging-submit-button',
          style: { backgroundColor: '#f3c314d4' },
        }}
      >
        <Form
          form={addPackagingForm}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onFinish={confirmAddPackaging}
          initialValues={{
            weightUnit: 'g',
            tier: 'Primary',
          }}
          id="add-packaging-form"
        >
          <Form.Item style={{ display: 'none' }} name="key">
            <Input type="hidden" />
          </Form.Item>

          <Form.Item
            label="Packaging Material"
            name="material"
            rules={[{ required: true, message: 'material is required' }]}
            tooltip="Specify the material name or choose one from the list"
          >
            <AutoComplete
              showSearch
              suffixIcon={null}
              allowClear
              style={{ width: 200 }}
              options={rawMaterialsPackaging}
              placeholder="PET"
              filterOption={(inputValue, option) =>
                option!.value
                  .toUpperCase()
                  .indexOf(inputValue.toUpperCase()) !== -1
              }
            />
          </Form.Item>

          <Form.Item
            label="Packaging Component"
            name="component"
            rules={[{ required: true, message: 'component is required' }]}
            tooltip="Specify the component name or choose one from the list (Ex: Cap, Label, Bottle, Box)"
          >
            <AutoComplete
              id='packaging-component'
              showSearch
              suffixIcon={null}
              allowClear
              style={{ width: 200 }}
              options={[
                { value: 'Cap' },
                { value: 'Label' },
                { value: 'Cork' },
                { value: 'Bottle' },
                { value: 'Jar' },
                { value: 'Carton' },
                { value: 'Box' },
                { value: 'Outer Box' },
              ]}
              placeholder="Bottle"
              filterOption={(inputValue, option) =>
                option!.value
                  .toUpperCase()
                  .indexOf(inputValue.toUpperCase()) !== -1
              }
            />
          </Form.Item>

          <Form.Item
              label="Description"
              name="description"
              tooltip="Description of the packaging material"
            >
              <Input
                style={{ float: 'left' }}
              />
            </Form.Item>

          <Row gutter={16}>
            <Col flex="1">
              <Form.Item
                label="Weight"
                name="weight"
                rules={[{ required: true, message: 'weight is required' }]}
                tooltip="Specify the weight with units"
              >
                <InputNumber
                  min="0"
                  style={{ width: '100%' }}
                  placeholder="90"
                />
              </Form.Item>
            </Col>
            <Col flex="1">
              <Form.Item
                name="weightUnit"
                label="Unit"
                rules={[{ required: true, message: 'unit is required' }]}
              >
                <Select
                  style={{ width: '100%' }}
                  options={[
                    {
                      label: 'Grams (g)',
                      value: 'g',
                    },
                    {
                      label: 'Kilograms (Kg)',
                      value: 'kg',
                    },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Packaging Tier"
            name="tier"
            tooltip="Specify the packaging tier"
          >
            <Radio.Group buttonStyle="solid">
              <Radio.Button id="packaging-tier-primary" value="Primary">
                Primary
              </Radio.Button>
              <Radio.Button id="packaging-tier-secondary" value="Secondary">
                Secondary
              </Radio.Button>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="dataField_advancedFields" valuePropName="checked">
            <Checkbox
              onChange={(e) =>
                setAdvancedAddPackagingFieldsIsChecked(e.target.checked)
              }
              checked={advancedAddPackagingFieldsIsChecked}
              style={{ float: 'left' }}
              id="advanced-packaging-fields-checkbox"
            >
              Advanced Fields
            </Checkbox>
          </Form.Item>
          <Form.Item
            label="Supplier Name"
            name="supplierName"
            tooltip="Specify the supplier name"
            hidden={!advancedAddPackagingFieldsIsChecked}
          >
            <Input style={{ width: '100%' }} placeholder="BTC" />
          </Form.Item>

          <Form.Item name="dataField_supplierLocation" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="supplierOrigin"
            label="Supplier Origin"
            tooltip="Specify the supplier location"
            hidden={!advancedAddPackagingFieldsIsChecked}
          >
            <Select
              showSearch
              suffixIcon={null}
              allowClear
              style={{ width: '100%', float: 'left' }}
              options={mapboxAutocompleteOptions}
              placeholder="London, United Kingdom"
              onSearch={handlePackagingSuppierLocationSearch}
              onSelect={(value, option) => {
                addPackagingForm.setFieldValue(
                  'dataField_supplierLocation',
                  option.data ?? null
                )
              }}
              notFoundContent={
                packagingSupplierLocationMapboxDataLoading ? (
                  <Spin size="small" />
                ) : null
              }
              onBlur={() => {
                setMapboxAutocompleteOptions([])
              }}
            />
          </Form.Item>
          <Form.Item
            name="recycledContent"
            label="Recycled Content"
            tooltip="Enter recycled content of the material value in percentage"
            hidden={!advancedAddPackagingFieldsIsChecked}
          >
            <InputNumber
              min="0"
              max="100"
              placeholder="85"
              addonBefore={<PercentageOutlined />}
            />
          </Form.Item>
        </Form>
      </Modal>
      <DataTable
        id={'add-packaging-table'}
        key={addPackagingTableKey}
        style={{
          paddingTop: '20px',
          paddingRight: '45px',
        }}
        paginate={false}
        bordered
        columns={packagingTableColumns}
        data={packagingTableDataSource}
      />
    </>
  )

  const addEolContent = (
    <>
      <p style={{ textAlign: 'left', marginRight: '20px', fontSize: '16px' }}>
        End of life (disposal) emissions are calculated based on the materials
        being disposed and government data sources on modes of disposal for
        material type in the target market
      </p>
      <br />
      <br />
    </>
  )

  const handleStepChange = async (step) => {
    if (step < currentStep) {
      // Moving backwards, allow without validation
      setCurrentStep(step)
    } else if (step > currentStep) {
      // Moving forwards, validate each step
      for (let i = currentStep; i < step; i++) {
        const canProceed = await validateStep(i)
        if (!canProceed) {
          return // Stop if validation fails
        }
      }
      setCurrentStep(step)
    }
  }

  const handleAddExchangeStepChange = async (step) => {
    if (step < currentAddExchangeStep) {
      // Moving backwards, allow without validation
      setCurrentAddExchangeStep(step)
    } else if (step > currentAddExchangeStep) {
      // Moving forwards, validate each step
      for (let i = currentAddExchangeStep; i < step; i++) {
        const canProceed = await validateAddExchangeStep(i)
        if (!canProceed) {
          return // Stop if validation fails
        }
      }
      setCurrentAddExchangeStep(step)
    }
  }

  const validateStep = async (step) => {
    switch (step) {
      case 0:
        try {
          await addProductForm.validateFields().then((values) => {
            setProductInfo(values)
            setCurrentStep(currentStep + 1)
            if (!manufacturingMethodTableDataSource.length)
              fetchManufacturingMethods()
            if (!consumerUseTableDataSource.length) fetchConsumerUse()
            return true
          })
        } catch (error) {
          message.error('Please fill in all required fields in Product Info')
          return false
        }
      case 1:
        return true
      case 2:
        if (!manufacturingMethodTableDataSource.length) {
          message.error('Please add at least one manufacturing method')
          return false
        }
        if (!transportationTableDataSource) fetchTransportSegments()
        return true
      case 3:
        if (!transportationTableDataSource.length) {
          message.error('Please add at least one transportation step')
          return false
        }
        return true
      default:
        return true
    }
  }

  const validateAddExchangeStep = async (step) => {
    switch (step) {
      case 0:
        if (!selectedExchangeActivity.activityName) {
          message.error('Please select an activity')
          return false
        }
        return true
      case 1:
        try {
          await addIntermediateExchangeForm.validateFields().then((values) => {
            setCurrentAddExchangeStep(currentAddExchangeStep + 1)
            return true
          })
        } catch (error) {
          message.error('Please fill in all required fields')
          return false
        }
      default:
        return true
    }
  }

  const next = async () => {
    const canProceed = await validateStep(currentStep)
    if (canProceed) {
      if (currentStep === 0 && !manufacturingMethodTableDataSource.length) {
        fetchManufacturingMethods()
      }
      if (currentStep === 2 && !transportationTableDataSource.length) {
        fetchTransportSegments()
      }
      setCurrentStep(currentStep + 1)
    }
  }

  const addExchangeStepNext = async () => {
    const canProceed = await validateAddExchangeStep(currentAddExchangeStep)
    if (canProceed) {
      setCurrentAddExchangeStep(currentAddExchangeStep + 1)
    }
  }

  const reviewContent = (
    <>
      {importFile ? (
        <>
          {productInfo && (
            <Descriptions
              labelStyle={{ width: '30%' }}
              column={1}
              style={{ paddingRight: '40px', textAlign: 'left' }}
              bordered
              title="Product Info"
            >
              {Object.entries(productInfo).map(
                ([key, value]) =>
                  !key.includes('dataField') &&
                  value && (
                    <Descriptions.Item
                      label={
                        <span style={{ fontWeight: 'bold', color: 'black' }}>
                          {camelCaseToNormalCase(key)}
                        </span>
                      }
                    >
                      {value as React.ReactNode}
                      {predictedFormValues[key] && (
                        <>
                          <Divider type="vertical" />
                          <PredictedIcon />
                        </>
                      )}
                    </Descriptions.Item>
                  )
              )}
            </Descriptions>
          )}
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Raw Materials
          </p>
          <DataTable
            key={addIngredientTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={reviewIngredientTableColumns}
            data={ingredientTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Manufacturing
          </p>
          <DataTable
            key={addManufacturingProcessTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={manufacturingTableColumns.slice(0, -1)}
            data={manufacturingMethodTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Transportation
          </p>
          <DataTable
            key={addTransportationTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={transportationTableColumns.slice(0, -1)}
            data={transportationTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Consumer Use
          </p>
          <DataTable
            key={consumerUseTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={consumerUseTableColumns}
            data={consumerUseTableDataSource}
          />
        </>
      ) : (
        <>
          {productInfo && (
            <Descriptions
              labelStyle={{ width: '30%' }}
              column={1}
              style={{ paddingRight: '40px', textAlign: 'left' }}
              bordered
              title="Product Info"
            >
              {Object.entries(productInfo).map(
                ([key, value]) =>
                  !key.includes('dataField') &&
                  value && (
                    <Descriptions.Item
                      label={
                        <span style={{ fontWeight: 'bold', color: 'black' }}>
                          {camelCaseToNormalCase(key)}
                        </span>
                      }
                    >
                      {value as React.ReactNode}
                      {predictedFormValues[key] && (
                        <>
                          <Divider type="vertical" />
                          <PredictedIcon />
                        </>
                      )}
                    </Descriptions.Item>
                  )
              )}
            </Descriptions>
          )}
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Raw Materials
          </p>
          <DataTable
            key={addIngredientTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={reviewIngredientTableColumns}
            data={ingredientTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Packaging
          </p>
          <DataTable
            key={addPackagingTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={packagingTableColumns.slice(0, -1)}
            data={packagingTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Manufacturing
          </p>
          <DataTable
            key={addManufacturingProcessTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={manufacturingTableColumns.slice(0, -1)}
            data={manufacturingMethodTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Transportation
          </p>
          <DataTable
            key={addTransportationTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={transportationTableColumns.slice(0, -1)}
            data={transportationTableDataSource}
          />
          <Divider orientation="left" />
          <p
            style={{
              fontWeight: '600',
              color: 'black',
              fontSize: '16px',
              textAlign: 'left',
            }}
          >
            Consumer Use
          </p>
          <DataTable
            key={consumerUseTableKey}
            style={{
              paddingTop: '20px',
              paddingRight: '45px',
            }}
            paginate={false}
            bordered
            columns={consumerUseTableColumns}
            data={consumerUseTableDataSource}
          />
        </>
      )}
    </>
  )

  let steps = [
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>Product Info</p>
        </>
      ),
      content: addProductContent,
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>Materials</p>
        </>
      ),
      content: (
        <>
          {addIngredientContent}
          <Divider orientation="left"></Divider>
          {addPackagingContent}
        </>
      ),
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>Manufacturing</p>
        </>
      ),
      content: <>{addManufacturingContent}</>,
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>
            Transportation
          </p>
        </>
      ),
      content: <>{addTransportationContent}</>,
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>Consumer Use</p>
        </>
      ),
      content:
        consumerUseTableDataSource.length === 0 ? (
          <>
            <p
              style={{
                textAlign: 'left',
                marginRight: '20px',
                fontSize: '16px',
              }}
            >
              Consumer use activities are modeled based on typical usage for the
              product category for the particular target market. Number of uses
              of a product are considered to calculate the emissions associated
              with a unit of product
            </p>
            <br />
            <br />
          </>
        ) : (
          <>
            <p style={{ textAlign: 'left', fontSize: '16px' }}>
              Let's enter the consumer use details for your product.
              <Tooltip title="Specify the typical usage for the product category.">
                <Button
                  icon={<QuestionCircleOutlined />}
                  type="link"
                  style={{ color: 'grey' }}
                ></Button>
              </Tooltip>
            </p>

            <DataTable
              id={'add-consumer-use-table'}
              key={consumerUseTableKey}
              style={{
                paddingTop: '20px',
                paddingRight: '45px',
              }}
              paginate={false}
              bordered
              columns={consumerUseTableColumns}
              data={consumerUseTableDataSource}
            />
          </>
        ),
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>End of Life</p>
        </>
      ),
      content: <>{addEolContent}</>,
    },
    {
      title: (
        <>
          <p style={{ width: 120, height: 40, paddingTop: 8 }}>Finish</p>
        </>
      ),
      content: reviewContent,
    },
  ]

  if (isImportComponent) {
    steps = [
      {
        title: (
          <>
            <p style={{ width: 120, height: 40, paddingTop: 8 }}>Components</p>
          </>
        ),
        content: (
          <>
            <p style={{ textAlign: 'left', fontSize: '16px' }}>
              Components Overview
            </p>
            <Form
              style={{ marginTop: '20px' }}
              form={addComponentForm}
              {...formItemLayout}
            >
              <Form.Item
                label={component ? 'Tags' : 'Product Name'}
                id="productName"
                name="productName"
                style={{ textAlign: 'left' }}
                rules={[
                  { required: true, message: 'Product name is required' },
                ]}
                tooltip="Enter comma separated tags"
              >
                <Input
                  id="add-component-product-name"
                  placeholder="Elegant Office Chair"
                />
              </Form.Item>
            </Form>
            <Divider />
            <DataTable
              key={addComponentTableKey}
              style={{
                paddingTop: '20px',
                paddingRight: '45px',
              }}
              paginate={false}
              bordered
              columns={componentTableColumns}
              data={componentTableDataSource}
            />
          </>
        ),
      },
    ]
  }

  if (basicProductEditing) {
    steps = [
      {
        title: (
          <>
            <p style={{ width: 120, height: 40, paddingTop: 8 }}>Product Info</p>
          </>
        ),
        content: addProductContent,
      },
    ]
  }

  const getBase64Data = (file, callback) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => callback(reader.result)
    reader.onerror = (error) => message.error('Error reading file: ' + error)
  }

  const hasUnknownValues = (value) => {
    return value?.toLowerCase().includes('unknown')
  }

  const handleFetchProductDetails = async (productId) => {
    try {
      const productInfo = await getProductDetails({
        variables: {
          productId,
          calculateEmissionsPerUnit: true
        },
      })

      return productInfo?.data?.getProductInfo ?? null
    } catch (error) {
      console.error('Error fetching product details: ' + error)
      return null
    }
  }

  const handleFetchComponents = async () => {
    const components = await getComponents({
      variables: { isComponent: true },
    })
    return components?.data?.getProducts ?? []
  }

  const renderProductDetails = async (_productDetails) => {
    if (_productDetails.components?.length) {
      setIsImportComponent(true)

      const components = []
      const missingComponents = []
      const existingComponents = await handleFetchComponents()
      const existingComponentIds = existingComponents.map((c) => c.productId.split("(")[0].trim())
      for (const component of _productDetails.components) {
        const componentId = component.componentId.split("(")[0].trim()
        const productInfo = await handleFetchProductDetails(
          component.componentId
        )
        if (!productInfo) {
          let isSupplierNameMismatch = false;
          if (existingComponentIds.includes(componentId)) {
            isSupplierNameMismatch = true
          }
          missingComponents.push({
            ...component,
            isSupplierNameMismatch
          })
        } else {
          components.push({
            key: uuidv4(),
            ...component,
            component: productInfo.productName,
            nodes: productInfo.nodes,
            edges: productInfo.edges,
          })
        }
      }

      if (missingComponents.length) {
        Modal.info({
          title: 'Missing Components',
          content: (
            <div>
              <p>The following components are missing:</p>
              <div style={{ maxHeight: '500px', overflow: 'auto', marginTop: '10px' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr>
                      <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>#</th>
                      <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Component ID</th>
                      <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Reason</th>
                    </tr>
                  </thead>
                  <tbody>
                    {missingComponents.map((item, index) => (
                      <tr key={index}>
                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{index + 1}</td>
                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.componentId}</td>
                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                          {item.isSupplierNameMismatch ? 'Supplier Mismatch' : 'Not Found'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ),
          okText: 'Continue',
          onOk() {
            if (components.length === 0) {
              navigate(routes.products())
              return;
            }
          },
        });
      }

      addComponentForm.setFieldValue('productName', 'Freedom Office Chair')

      setComponentTableDataSource(components)
      setAddComponentTableKey(uuidv4())

      setExtractFileIsLoading(false)
      setIsFileImportComplete(true)

      return
    }

    let factoryCountryInfo = null
    let productDetails = { ..._productDetails }

    //TODO: Cleanup
    if (orgMemberInfo?.orgMetadata?.processModelEnabled) {
      if (!productDetails.productName)
        productDetails.productName = 'Freedom Office Chair'
      if (!productDetails.productID) productDetails.productID = '123456'
      if (!productDetails.annualSalesVolume)
        productDetails.annualSalesVolume = '1'

      if (!productDetails.factoryCountry) {
        productDetails.factoryCountry = 'United States'
        productDetails.factoryCity = 'Piscataway,  New Jersey'
      }
    }

    setProcessModelData(productDetails)

    try {
      factoryCountryInfo = countryCodeLookup.byCountry(
        productDetails.factoryCountry
      )
    } catch (error) {
      console.error('Error looking up country code: ' + error)
    }

    let _factoryLocation = null

    if (factoryCountryInfo) {
      _factoryLocation = _buildSupplierLocation(
        productDetails.factoryCity,
        productDetails.factoryCountry
      )
    }

    const fileUploadDataMapping = {
      factoryLocation:
      _factoryLocation && _factoryLocation.label,
      factoryLocationData:
      _factoryLocation && _factoryLocation.data,
      targetMarketLocation: 'United States',
      targetMarketLocationData: JSON.stringify({
        district: 'Washington DC',
        region: '',
        country: 'United States',
        country_code: 'USA',
        latitude: 39.3812661305678,
        longitude: -97.9222112121185,
      }),
    }

    const productName = !hasUnknownValues(productDetails.productName)
      ? productDetails.productName
      : ''

    const category = await predictProductCategory({
      variables: {
        productName,
      },
    })

    const ProductInfo = {
      productName: productName,
      category: category.data.predictProductCategory.name ?? '',
      sku: productDetails.productID ?? '',
      annualSalesVolume: productDetails.annualSalesVolume,
      factoryLocation: fileUploadDataMapping.factoryLocation,
      dataField_factoryLocation: fileUploadDataMapping.factoryLocationData,
      targetMarketLocation: fileUploadDataMapping.targetMarketLocation,
      dataField_targetMarketLocation:
        fileUploadDataMapping.targetMarketLocationData,
    }

    if (ProductInfo.sku || ProductInfo.annualSalesVolume) {
      setAddProductOptioinalDataCollapseKey(['1'])
    }

    addProductForm.setFieldsValue(ProductInfo)

    const ingredientsData = []
    const rawMaterialNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'material'
    )
    for (const ingredient of rawMaterialNodes) {
      let supplierLocationData = null
      if (ingredient.location.country) {
        supplierLocationData = _buildSupplierLocation(
          ingredient.location.city,
          ingredient.location.country
        )
      }

      let ingredientData = {
        key: uuidv4(),
        ingredient: ingredient.name,
        component: ingredient.component,
        description: ingredient.description,
        weight: ingredient.amount,
        weightUnit: ingredient.unit,
        casNo: null,
        supplierName: null,
        supplierOrigin: supplierLocationData
          ? supplierLocationData?.label
          : null,
        dataField_supplierLocation: supplierLocationData
          ? supplierLocationData?.data
          : null,
        recycledContent: 0,
        emissionsFactorMatch: null,
        emissionsFactorMatches: [],
        emissionsFactorMatchActivityName: null,
        emissionsFactorMatchSource: null,
        predictSourceIsLoading: !supplierLocationData ? true : false,
        predictEmissionsFactorMatchIsLoading: !supplierLocationData
          ? false
          : true,
        location: {
          city: ingredient?.location?.city,
          country: ingredient?.location?.country,
        },
      }

      ingredientsData.push(ingredientData)
    }
    setIngredientTableDataSource(ingredientsData)
    setAddIngredientTableKey(uuidv4())
    for (const ingredient of ingredientsData) {
      if (!ingredient.supplierOrigin) {
        handlePredictIngredientSource(ingredient, ProductInfo.category)
      } else {
        handlePredictIngredientEmissionsFactors(
          ingredient,
          ProductInfo.category
        )
      }
    }

    //Packaging
    const packagingData = []
    const packagingNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'packaging'
    )
    for (const packagingNode of packagingNodes) {
      let supplierLocationData = null
      if (packagingNode.location.country) {
        supplierLocationData = _buildSupplierLocation(
          packagingNode.location.city,
          packagingNode.location.country
        )
      }
      const packagingMaterial = {
        key: uuidv4(),
        material: packagingNode.name,
        component: packagingNode.component,
        description: packagingNode.description,
        weight: packagingNode.amount,
        weightUnit: packagingNode.unit,
        tier: packagingNode.packagingLevel,
        supplierName: null,
        supplierOrigin: supplierLocationData
          ? supplierLocationData?.label
          : null,
        dataField_supplierLocation: supplierLocationData
          ? supplierLocationData?.data
          : null,
        emissionsFactorMatch: null,
        emissionsFactorMatches: [],
        emissionsFactorMatchActivityName: null,
        emissionsFactorMatchSource: null,
        predictSourceIsLoading: !supplierLocationData ? true : false,
        predictEmissionsFactorMatchIsLoading: !supplierLocationData
          ? false
          : true,
        location: {
          city: packagingNode.location?.city,
          country: packagingNode.location?.country,
        },
      }
      packagingData.push(packagingMaterial)
    }
    setPackagingTableDataSource(packagingData)
    setAddPackagingTableKey(uuidv4())
    for (const packaging of packagingData) {
      if (!packaging.supplierOrigin) {
        handlePredictPackagingSource(packaging)
      } else {
        handlePredictPackagingEmissionsFactors(packaging)
      }
    }

    //Manufacturing
    const manufacturingData = []
    const manufacturingNodes = productDetails.nodes.filter(
      (x) => x.nodeType === 'production'
    )
    for (const manufacturingNode of manufacturingNodes) {
      const manufacturingProcess = {
        key: uuidv4(),
        activityName: manufacturingNode.name,
        activityType: 'Total Carbon Emissions',
        amount: null,
        unit: manufacturingMethodActivityUnitMapping['Total Carbon Emissions'],
        amountOfProductKg: 1000,
        predictEmissionsFactorMatchIsLoading: true,
        location: {
          city: manufacturingNode.location.city,
          country: manufacturingNode.location.country,
        },
      }
      manufacturingData.push(manufacturingProcess)
    }

    setManufacturingMethodTableDataSource(manufacturingData)
    setAddManufacturingProcessTableKey(uuidv4())

    for (const manufacturingProcess of manufacturingData) {
      handlePredictManufacturingEmissionsFactors(manufacturingProcess)
    }

    fetchConsumerUse()

    setExtractFileIsLoading(false)
    setIsFileImportComplete(true)
  }

  const renderComponentDetails = async (_componentDetails) => {

    const processComponents = async(componentDetailsToProcess) => {

      if (_componentDetails.warnings.length) {
        const warningColumns = [
          {
            title: 'Warning',
            dataIndex: 'message',
            key: 'message',
            render: (text) => <span style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>{text}</span>,
          },
        ];

        const warningDataSource = _componentDetails.warnings.map((warning, index) => ({
          key: index,
          message: warning,
        }));

        Modal.info({
          title: 'Warnings',
          width: 600,
          content: (
            <div>
              <p>Some warnings were generated while processing the file:</p>
              <div style={{ maxHeight: '400px', overflow: 'auto', marginTop: '10px' }}>
                <Table
                  columns={warningColumns}
                  dataSource={warningDataSource}
                  pagination={false}
                  size="small"
                  bordered
                  style={{ whiteSpace: 'pre-wrap' }}
                />
              </div>
            </div>
          ),
          okText: 'Continue',
          onOk() {
            return
          },
        });
      }

      const componentDetails = componentDetailsToProcess.map((component) => {
        return {
          key: uuidv4(),
          componentId: component.componentId,
          component: component.componentName.trim(),
        }
      });

      setComponentTableDataSource(componentDetails);
      setAddComponentTableKey(uuidv4());

      let componentData = [];
      let factoryLocation = {
        country: "Taiwan",
        countryCode: "TW",
        default: true,
      };
      for (const component of componentDetailsToProcess) {
        const nodes = [];

        const edges = component.edges.map((edge) => {
          return {
            from_node_id: edge.fromNodeId,
            to_node_id: edge.toNodeId,
          }
        });

        for (const node of component.nodes) {
          if (!["transportation", "bundle"].includes(node.nodeType)) {

            if (factoryLocation.default) {
              const lastProductionNode = getDescendantNodeOfType({
                nodes: component.nodes,
                edges: component.edges,
                node,
                nodeTypes: ['production'],
              })
              if (lastProductionNode?.location?.country) {
                const countryInfo = await fetchPlacesAutoComplete(lastProductionNode.location?.country, '', 'country')
                if (countryInfo?.length) {
                  const countryInfoData = JSON.parse(countryInfo[0]?.data)
                  factoryLocation.country = lastProductionNode.location?.country
                  factoryLocation.countryCode = countryInfoData.country_code
                } else {
                  console.error('Failed to find country code for: ' + lastProductionNode.location?.country)
                }
                factoryLocation.default = false
              }
            }

            const emissionsFactorMatch = await handlePredictEmissionsFactors({
              chemicalName: node.name,
              geography: factoryLocation.countryCode,
              geographyModeling: true,
              unit: node.unit ?? 'g',
            });

            let nodeLocation = {
              city: node.location?.city,
              country: node.location?.country,
            };

            if (['material', 'packaging'].includes(node.nodeType)) {

              if (!node.location?.country) {
                // First, try to find a production node
                let destinationNode = getDescendantNodeOfType({
                  nodes: component.nodes,
                  edges: component.edges,
                  node,
                  nodeTypes: ['production'],
                })

                // If no production node found, try to find a bundle node
                if (!destinationNode) {
                  destinationNode = getDescendantNodeOfType({
                    nodes: component.nodes,
                    edges: component.edges,
                    node,
                    nodeTypes: ['bundle'],
                  })
                }

                if (!destinationNode) {
                  console.log(node)
                  console.log(component)
                  console.error('No destination node found for material or packaging node')
                }

                if (destinationNode) {
                  nodeLocation = {
                    city: destinationNode.location?.city,
                    country: destinationNode.location?.country,
                  }
                  const materialSource = await predictIngredientSource({
                    variables: {
                      ingredientName: node.name,
                      productCategory: 'NA', //TODO: Remove productCategory
                      country: destinationNode.location?.country,
                    },
                  })
                  if (materialSource?.data?.predictIngredientSource) {
                    nodeLocation = {
                      city: null,
                      country:
                        materialSource.data.predictIngredientSource.country,
                    }
                  }
                }
              } else {

                const successorNodeIds = component.edges.filter(edge => edge.fromNodeId === node.id).map(x => x.toNodeId)
                const connectedBundleNode = component.nodes.find(node => {
                  if (node.nodeType == "bundle" && successorNodeIds.includes(node.id)) {
                    return node
                  }
                })

                if (connectedBundleNode) {
                  const connectedMaterialEdges = component.edges.filter(e => e.toNodeId === connectedBundleNode.id)
                  const connectedMaterialNodes = component.nodes.filter(n => {
                    return ["material", "packaging"].includes(n.nodeType) && connectedMaterialEdges.some(e => e.fromNodeId === n.id)
                  })
                  const connectedMaterialNodeWithLocation = connectedMaterialNodes.find(n => n.location?.country !== null)
                  if (!connectedMaterialNodeWithLocation) {
                    const materialSource = await predictIngredientSource({
                      variables: {
                        ingredientName: node.name,
                        productCategory: 'NA', //TODO: Remove productCategory
                        country: factoryLocation.country,
                      },
                    })
                    if (materialSource?.data?.predictIngredientSource) {
                      nodeLocation = {
                        city: null,
                        country:
                          materialSource.data.predictIngredientSource.country,
                      }
                    }
                  } else {
                    nodeLocation = {
                      city: connectedMaterialNodeWithLocation.location?.city,
                      country: connectedMaterialNodeWithLocation.location?.country,
                    }
                  }
                }

              }
              if (!nodeLocation.country) {
                nodeLocation = {
                  city: null,
                  country: "Taiwan",
                }
              }
            }

            // if production node has no location, set it to Taiwan
            if (!nodeLocation.country) {
              console.log(`Node ${node.name} - ${node.nodeType}(Component ${component.componentId}) has no location, setting to Taiwan`)
              nodeLocation = {
                city: null,
                country: "Taiwan",
              }
            }

          nodes.push({
            id: node.id,
            name: node.name.trim(),
            node_type: node.nodeType,
            location: nodeLocation,
            amount: node.amount,
            component_name: node.component,
            description: node.description,
            unit: node.unit ?? 'g',
            scrap_rate: node.scrapRate ?? 0,
            scrap_fate: node.scrapFate,
            emissions_factor: {
              activityName: emissionsFactorMatch?.activityMatch?.activityName,
              referenceProduct:
                emissionsFactorMatch?.activityMatch?.referenceProduct,
              geography: emissionsFactorMatch?.activityMatch?.geography,
              source: emissionsFactorMatch?.activityMatch?.source,
              modified: emissionsFactorMatch?.activityMatch?.modified ?? false,
              unit: emissionsFactorMatch?.activityMatch?.unit,
              kgCO2e: emissionsFactorMatch?.activityMatch?.kgCO2e,
              exchanges: emissionsFactorMatch?.activityMatch?.exchanges ?? [],
              elementalEfValues: emissionsFactorMatch?.activityMatch.elementalEfValues,
            },
            quantity: node.quantity ?? 1,
          })
        } else {
          nodes.push({
            id: node.id,
            name: node.name.trim(),
            component_name: node.component,
            description: node.description,
            node_type: node.nodeType,
            location: node.location?.country ? {
              city: node.location.city,
              country: node.location.country,
            } : null,
            amount: node.amount,
            unit: node.unit ?? 'g',
            quantity: node.quantity ?? 1,
          })
        }

      }

      nodes.filter(node => node.node_type === 'bundle').map(node => {
        if (!node.location?.country) {
          const materialNode = getAncestorNodeOfType(
            nodes,
            edges,
            node,
            ['material', 'packaging'],
            'node_type'
          )
          node.location = materialNode.location
        }
        return node
      })

        componentData.push({
          component_id: component.componentId,
          component_name: component.componentName.trim(),
          nodes,
          edges,
        });
      }

      setComponentDetails(componentData);

      setExtractFileIsLoading(false);
      setIsFileImportComplete(true);
    }

    const duplicateComponents = []

    for (const component of _componentDetails.components) {
      const componentInfo = await handleFetchProductDetails(
        component.componentId
      )

      if (componentInfo) {
        duplicateComponents.push({
          componentId: component.componentId,
          component: component.componentName.trim()
        })
      }
    }

    if (duplicateComponents.length > 0) {
      Modal.info({
        title: 'Duplicate Components Detected',
        content: (
          <div>
            <p>The following duplicate components will be skipped:</p>
            <div style={{ maxHeight: '200px', overflow: 'auto', marginTop: '10px' }}>
              <ul>
                {duplicateComponents.map((item, index) => (
                  <li key={index}>{item.component} (ID: {item.componentId})</li>
                ))}
              </ul>
            </div>
          </div>
        ),
        okText: 'Continue',
        onOk() {
          const filteredComponentDetails = _componentDetails.components.filter(component =>
            !duplicateComponents.some(
              dupComp => dupComp.componentId === component.componentId
            )
          );

          if (filteredComponentDetails.length === 0) {
            navigate(routes.component())
            return;
          }

          processComponents(filteredComponentDetails);

        },
      });
    } else {
      await processComponents(_componentDetails.components);
    }
  }

  const handleFileUpload = async () => {
    setExtractFileIsLoading(true)
    setFileUploadPercent(0)

    try {
      if (!(uploadedFile.base64Data ?? '').length) {
        throw new Error('Please upload a valid file to continue')
      }

      let errorMessage = 'Looks like the file is empty or not a valid file'

      if (component) {
        const response = await extractComponentsFromFile({
          variables: {
            base64Data: uploadedFile.base64Data,
            contentType: uploadedFile.contentType,
            component,
          },
        })

        if (!response?.data?.extractComponentsFromFile) {
          if (response?.error && response?.error?.message) {
            errorMessage = response.error.message
          }
          throw new Error(errorMessage)
        }

        await renderComponentDetails(response.data.extractComponentsFromFile)
        return
      }

      const response = await extractFile({
        variables: {
          base64Data: uploadedFile.base64Data,
          contentType: uploadedFile.contentType,
          component,
        },
      })

      if (!response?.data?.extractFile) {
        if (response?.error && response?.error?.message) {
          errorMessage = response.error.message
        }
        throw new Error(errorMessage)
      }

      await renderProductDetails(response.data.extractFile)
    } catch (error) {
      notification.error({
        message: 'Error Extracting File',
        description: error?.message,
        duration: 0,
      })
      setExtractFileIsLoading(false)
    }
  }

  const uploadFileProps: UploadProps = {
    name: 'product_pdf',
    accept:
      'application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, text/csv, text/tab-separated-values, text/html, application/vnd.oasis.opendocument.text',
    multiple: false,
    beforeUpload: (file) => {
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
        'text/tab-separated-values',
        'text/html',
        'application/vnd.oasis.opendocument.text',
      ]
      const isAllowedType = allowedTypes.includes(file.type)

      if (!isAllowedType) {
        message.error(
          `${file.name} is not a valid file. Please upload a file in one of the supported formats: Excel, PDF, DOCX, CSV, TSV, EPUB, HTML, or ODT.`
        )
        return false
      }

      getBase64Data(file, (base64) => {
        setUploadedFile({
          base64Data: base64,
          contentType: file.type,
          name: file.name,
        })
        setShowFilePreview(true)
      })
      return false
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files)
    },
  }

  const updateProductDetails = async () => {
    let productInfo = {
      ..._productDetails,
      ...addProductForm.getFieldsValue(),
    }

    const targetMarketLocation = JSON.parse(
      productInfo.dataField_targetMarketLocation
    )

    let productId = null
    try {
      await backupProduct({
        variables: {
          productId: _productDetails.productId,
        },
      })

      const productResponse = await handleCreateProduct({
        product: productInfo,
        oldProductId: _productDetails.productId,
      })
      productId = productResponse.createProduct.productId

      await createProductProcessModel({
        variables: {
          productId: productId,
          processModel: {
            nodes: productInfo.nodes.map((node) => {

              const _node = transformNode({
                ...node,
                location: node.location
                ? {
                    city: ['eol', 'use'].includes(node.nodeType)
                      ? targetMarketLocation.district
                      : node.location.city,
                    country: ['eol', 'use'].includes(node.nodeType)
                      ? targetMarketLocation.country
                      : node.location.country,
                  }
                : null,
              })

              return _node
            }),
            edges: productInfo.edges.map((edge) => {
              return {
                from_node_id: productInfo.nodes.find(
                  (node) => node.id === edge.fromNodeId
                ).id,
                to_node_id: productInfo.nodes.find(
                  (node) => node.id === edge.toNodeId
                ).id,
              }
            }),
          },
        },
      })

      await handleActivateProduct(productId)

      notification.success({
        placement: 'topRight',
        message: 'Updated Product',
        description: `Product ${productResponse.createProduct.productName} updated successfully`,
        duration: 0,
      })

      await deleteProduct({
        variables: {
          productId: `${_productDetails.productId}_backup`,
        },
      })

      setTimeout(() => {
        navigate(routes.products())
      }, 2000)
    } catch (error) {
      console.error(error)
      if (productId) {
        await deleteProduct({
          variables: { productId: productId },
        })
      }

      await restoreProduct({
        variables: {
          productId: `${_productDetails.productId}_backup`,
        },
      })

      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message

      return notification.error({
        placement: 'topRight',
        message: 'Error updating product',
        description: errorMessage,
        duration: 0,
      })
    }
  }

  const confirmCreateProduct = async () => {
    if (isImportComponent) {
      if (component) {
        return addComponent()
      }
      return addParts()
    }

    if (basicProductEditing) {
      return updateProductDetails()
    }

    return addProduct(productInfo)
  }

  return mapboxAccessTokenLoading || editProductIsLoading ? (
    <LoadingSkeleton />
  ) : (
    <>
      <Modal
        maskClosable={false}
        closable={false}
        title={
          extractFileIsLoading ? 'Extracting Data From File' : 'Upload File'
        }
        centered
        open={!isFileImportComplete}
        okText={showFilePreview ? 'Confirm' : 'Upload'}
        cancelText={showFilePreview ? 'Cancel' : 'Exit'}
        cancelButtonProps={{
          loading: extractFileIsLoading,
          style: {
            display: extractFileIsLoading ? 'none' : 'inline-block',
            fontWeight: 600,
            color: '#f3c314d4',
            height: '30px',
            border: '1px solid #f3c314d4',
            backgroundColor: 'white',
          },
        }}
        okButtonProps={{
          id: 'confirm-upload-button',
          loading: extractFileIsLoading,
          style: {
            display: showFilePreview
              ? extractFileIsLoading
                ? 'none'
                : 'inline-block'
              : 'none',
            fontWeight: 600,
            height: '30px',
            backgroundColor: '#f3c314d4',
          },
        }}
        onCancel={() => {
          if (showFilePreview) {
            setShowFilePreview(false)
          } else {
            navigate(routes.products())
          }
        }}
        onOk={handleFileUpload}
      >
        {showFilePreview ? (
          extractFileIsLoading ? (
            <>
              <Progress
                strokeColor={'#ffb82b'}
                percent={fileUploadPercent}
                status="active"
              />
              <p>{fileUploadStatusMessage || ''}</p>
              <p style={{ marginLeft: '15%', marginBottom: 10, marginTop: 10 }}>
                <PredictionLoadingIcon />
              </p>
            </>
          ) : (
            !["application/vnd.ms-excel",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
              "text/csv"].includes(uploadedFile.contentType) ? (
            <>
              <p style={{ fontSize: '16px', marginTop: '15px' }}>
                Review your uploaded file and click on confirm to proceed
              </p>
              <FileViewer
                fileData={uploadedFile.base64Data}
                contentType={uploadedFile.contentType}
              />
            </>
            ) : (
              <>
                <p style={{ fontSize: '16px', marginTop: '15px' }}>
                  Are you sure you want to upload this file ?<br /><small>{uploadedFile.name}</small>
                </p>
                <p className="ant-upload-drag-icon" style={{ fontSize: '50px', color: '#1677ff', marginLeft: '45%' }}>
                <FileExcelOutlined />
                </p>
              </>
            )
          )
        ) : (
          <>
            <Paragraph>
              <p>
                <span style={{ fontWeight: 'bold' }}>Note:</span> Make sure the
                file contains clear and structured information for accurate
                extraction.
              </p>
              <p>
                <span style={{ fontWeight: 'bold' }}>Supported Files:</span>{' '}
                Excel, PDF, DOCX, CSV, TSV, EPUB, HTML or ODT
              </p>
            </Paragraph>
            <Dragger {...uploadFileProps}>
              <p className="ant-upload-drag-icon">
                <FileOutlined />
                <FilePdfOutlined />
                <FileTextOutlined />
                <FileExcelOutlined />
              </p>
              <p className="ant-upload-text">
                Click or drag file to this area to upload
              </p>
            </Dragger>
          </>
        )}
      </Modal>
      <Row>
        <Col flex="auto">
          <Breadcrumb
            items={[
              {
                title: <a href={routes.products()}>Inventory</a>,
              },
              {
                title: (() => {
                  if (importFile) {
                    return <p>Import {component ? 'Component' : 'Product'}</p>
                  } else if (editMode) {
                    return <p>Edit {component ? 'Component' : 'Product'}</p>
                  } else {
                    return <p>New {component ? 'Component' : 'Product'}</p>
                  }
                })(),
              },
            ]}
          ></Breadcrumb>
        </Col>
      </Row>
      <Divider orientation="left"></Divider>
      <Layout>
        <Content
          style={{
            backgroundColor: 'white',
            borderRadius: '10px',
            filter: isFileImportComplete ? 'none' : 'blur(2px)',
          }}
        >
          <Steps
            labelPlacement="vertical"
            style={{
              marginTop: '20px',
              paddingLeft: '20px',
              paddingRight: '20px',
              display: isImportComponent || basicProductEditing ? 'none' : 'flex',
            }}
            size="small"
            direction="horizontal"
            current={currentStep}
            items={steps}
            onChange={(step) => handleStepChange(step)}
          />
          <Divider orientation="left"></Divider>
          <div
            style={{
              textAlign: 'center',
              marginLeft: '40px',
            }}
          >
            {steps[currentStep].content}
          </div>
          <div
            style={{
              marginTop: '20px',
              marginRight: '40px',
              marginBottom: '70px',
            }}
          >
            <Button
              style={{
                float: 'left',
                marginLeft: 20,
                fontWeight: 600,
                width: '100px',
                height: '30px',
              }}
              id="cancel-button"
              onClick={() => window.history.back()}
            >
              Cancel
            </Button>
            {currentStep < steps.length - 1 && (
              <Button
                style={{
                  float: 'right',
                  color: 'white',
                  fontWeight: 600,
                  width: '100px',
                  height: '30px',
                  backgroundColor: '#f3c314d4',
                }}
                id="next-button"
                loading={formIsLoading}
                onClick={() => next()}
              >
                Next
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button
                id="finish-button"
                style={{
                  float: 'right',
                  color: 'white',
                  fontWeight: 600,
                  height: '30px',
                  backgroundColor: '#f3c314d4',
                }}
                loading={
                  formIsLoading ||
                  predictIngredientSourceIsLoading ||
                  predictEmissionsFactorsIsLoading ||
                  createProductIsLoading ||
                  createSupplierIsLoading ||
                  createProductProcessModelIsLoading ||
                  activateProductIsLoading ||
                  deleteProductLoading ||
                  backupProductIsLoading ||
                  restoreProductIsLoading ||
                  createEmissionsFactorIsLoading
                }
                onClick={confirmCreateProduct}
              >
                Save
              </Button>
            )}
            {currentStep > 0 && (
              <Button
                style={{
                  margin: '0 8px',
                  float: 'right',
                  fontWeight: 600,
                  width: '100px',
                  color: '#f3c314d4',
                  height: '30px',
                  border: '1px solid #f3c314d4',
                  backgroundColor: 'white',
                }}
                loading={formIsLoading}
                onClick={() => prev()}
              >
                Back
              </Button>
            )}
          </div>
          <EmissionsFactorSelector
            isOpen={emissionsFactorMatchesDrawerIsOpen}
            onClose={() => setEmissionsFactorActivityMatchesDrawerIsOpen(false)}
            selectedItem={selectedChemicalName}
            onEmissionsFactorUpdate={handleEmissionsFactorUpdate}
            initialEmissionsFactor={selectedChemicalName.currentEmissionsFactor}
            initialEmissionsFactorMatches={
              selectedChemicalName.emissionsFactorMatches
            }
            editMode={editMode}
            geographyModelingEnabled={true}
            productCategoryEnabled={true}
            activityType={selectedChemicalName.activityType}
          />
        </Content>
      </Layout>
    </>
  )
}

export default AddProduct
