# Issue Summary: Test Failure in Add Product E2E Test

## Issue Details
- **Test Suite**: Playwright E2E
- **Test File**: `/tests/e2e/02-01-inventory-add-product.spec.ts`
- **Test Name**: 'Add Product'
- **Failure Type**: Test timeout
- **Timeout Duration**: 680,000ms (11.3 minutes)

## Failure Point
The test fails when trying to interact with a row containing "trichloroacetic acid" after searching for "citric acid" in the emissions factor selector.

```typescript
await page.getByPlaceholder('Name').fill('citric acid');
await page.click('button#search-ef-button');
await page.getByRole('row', { name: 'trichloroacetic acid' }).first().getByLabel('', { exact: true }).check();
```

## Technical Flow Analysis

### Component Flow
1. User enters "citric acid" in the search field
2. Clicks search button (button#search-ef-button)
3. EmissionsFactorSelector component triggers handleEmissionsFactorSearch
4. GraphQL mutation PREDICT_EMISSIONS_FACTORS_QUERY is called

### API Flow
1. GraphQL endpoint: `/raw-materials/activities/recommendations`
2. Request Parameters:
```typescript
{
  chemical_name: 'citric acid',
  product_category: null,
  cas_number: null,
  geography: 'GLO',  // default value
  geography_modeling: false,
  unit: null
}
```

### Expected Response Structure
```typescript
{
  data: {
    predictEmissionsFactors: {
      matchedActivity: {
        activityUUID: string,
        activityName: string,
        referenceProduct: string,
        productInformation: string,
        similarity: number,
        curated: boolean,
        geography: string,
        source: string,
        unit: string,
        kgCO2e: number
      },
      recommendations: Array<{
        // Similar structure to matchedActivity
      }>,
      confidence: string,
      explanation: string
    }
  }
}
```

## Probable Root Causes

### 1. API Response Issues
- API might not be returning "trichloroacetic acid" in recommendations
- Response could be empty or malformed
- API might be timing out
- Backend service might be unavailable or returning errors

### 2. Data Issues
- Test data might be outdated or inconsistent
- "trichloroacetic acid" might not be a valid recommendation for "citric acid"
- Database might not contain expected emissions factor data

### 3. UI/Component Issues
- EmissionsFactorSelector component might not be properly rendering the response
- Table data might not be updated after the API response
- Loading state might not be properly handled

### 4. Timing Issues
- Test might be trying to access the row before data is loaded
- Race condition between API response and UI update

## Suggested Debugging Steps

1. **Add Response Logging**
```typescript
page.on('response', async response => {
  if (response.url().includes('/raw-materials/activities/recommendations')) {
    console.log(await response.json());
  }
});
```

2. **Add Table Content Logging**
```typescript
// Before the failing line
console.log('Current table contents:', await page.$$eval('table tr', rows =>
  rows.map(row => row.textContent)
));
```

3. **Add Loading State Check**
```typescript
await page.waitForSelector('[data-loading="false"]', { state: 'attached' });
```

## Recommended Fixes

1. **Short Term Fix**
Add proper waiting mechanisms:
```typescript
// Wait for API response and table update
await page.waitForResponse(response =>
  response.url().includes('/raw-materials/activities/recommendations')
);
await page.waitForSelector('.ant-table-row', { state: 'attached' });
```

2. **Long Term Fix**
- Add better error handling in the EmissionsFactorSelector component
- Add data-testid attributes for more reliable element selection
- Add retry mechanism for API calls
- Update test data to ensure consistent recommendations
- Add monitoring for API response times and success rates

## Impact
- This test failure blocks the CI/CD pipeline
- Affects the testing of the product creation flow
- May indicate issues with the emissions factor recommendation system

## Additional Context
- Test is part of a larger E2E suite testing product creation
- The emissions factor selection is a critical part of the product creation flow
- System uses AI/ML for recommendations which may introduce variability

## Related Components
- EmissionsFactorSelector (`web/src/components/EmissionsFactorSelector/EmissionsFactorSelector.tsx`)
- GraphQL mutations in `web/src/utils/graphql.ts`
- Backend service endpoint `/raw-materials/activities/recommendations`

## Next Steps
1. Verify API response data
2. Add comprehensive logging
3. Update test to handle loading states
4. Consider updating test data
5. Add retry mechanism for flaky operations
