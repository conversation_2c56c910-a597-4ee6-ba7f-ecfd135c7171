# CarbonBright Web App Architecture

## Overview
The CarbonBright Web App is a RedwoodJS application designed for Life Cycle Assessment (LCA) and carbon footprint management. It consists of a web frontend and an API backend that communicates with external services for LCA calculations and ML model predictions.

## Technology Stack
- **Framework**: RedwoodJS
- **Frontend**: React, Ant Design
- **API**: GraphQL
- **Authentication**: PropelAuth
- **External Services**:
  - LCA API (`LCA_API_ENDPOINT`)
  - ML Models service (`ML_MODELS_ENDPOINT`)
  - Mapbox for geographical visualization
- **Error Tracking**: Sentry

## Core Workflows

### 1. Product Upload and Emissions Factor Matching

#### Workflow Overview
1. User uploads a document (PDF, Excel, CSV, etc.) containing product details
2. System extracts product information including materials and manufacturing processes
3. System automatically searches for matching emissions factors
4. User can manually search and refine emissions factor matches

#### Components and Services Involved

##### Document Upload
- **UI Component**: `AddProduct.tsx`
  ```tsx
  // File upload configuration
  const uploadFileProps: UploadProps = {
    name: 'product_pdf',
    accept: 'application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, text/csv, text/tab-separated-values, text/html, application/vnd.oasis.opendocument.text',
    // ...
  }

  // Handle file upload
  const handleFileUpload = async () => {
    setExtractFileIsLoading(true)
    // ...
    const response = await extractFile({
      variables: {
        base64Data: uploadedFile.base64Data,
        contentType: uploadedFile.contentType,
      }
    })
    // ...
    await renderProductDetails(response.data.extractFile)
  }
  ```

- **GraphQL Query**: `EXTRACT_FILE_QUERY` in `AddProduct.tsx`
  ```graphql
  const EXTRACT_FILE_QUERY = gql`
    query ExtractFileQuery($base64Data: String!, $contentType: String!) {
      extractFile(base64Data: $base64Data, contentType: $contentType) {
        productName
        productID
        annualSalesVolume
        factoryCity
        factoryCountry
        nodes {
          id
          name
          component
          description
          packagingLevel
          nodeType
          location {
            city
            country
          }
          amount
          quantity
          unit
          scrapRate
          scrapFate
        }
        edges {
          fromNodeId
          toNodeId
        }
        components {
          componentId
          quantity
        }
      }
    }
  `
  ```

- **Backend Service**: `extract_file.ts`
  ```typescript
  export const extractFile = async ({ base64Data, contentType }) => {
    try {
      const blob = base64ToBlob(base64Data, contentType)
      const formData = new FormData()
      formData.append('file', blob, 'product_info')

      let headers = {
        'Content-Type': 'multipart/form-data',
        ...(context.currentUser.orgMemberInfo.orgMetadata
          ?.processModelEnabled && {
          'X-Use-Graph-Extraction': 'true',
          'X-Parts-Only': 'true',
        }),
      }

      const response = await axios.post(
        `${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/`,
        formData,
        { headers }
      )
      // Process response...
    }
  }
  ```

##### Emissions Factor Search
- **UI Components**:
  - `ActivityDatasetsCell.tsx` - Displays emissions factor search results
    ```tsx
    export const QUERY = gql`
      query GetEmissionsFactors($activityName: String, $geography: [String], $source: [String], $unit: [String]) {
        getEmissionsFactors(
          activityName: $activityName
          geography: $geography
          source: $source
          unit: $unit
        ) {
          efId
          activityName
          description
          referenceProduct
          geography
          source
          unit
          isTenant
        }
      }
    `
    ```

  - `EmissionsFactorSelector.tsx` - Component for selecting emissions factors
    ```tsx
    // Search functionality
    const onSearch = async (value) => {
      // Validation and quota checks
      // ...
      const response = await searchEmissionsFactors({
        variables: { activityName: value }
      })
      // Process results
    }

    // Search input
    <Input.Search
      placeholder="Enter material or process (e.g., 'citric acid, steel production')"
      enterButton={<Button type="primary" icon={<SearchOutlined />}>Search</Button>}
      size="large"
      value={searchValue}
      onChange={(e) => setSearchValue(e.target.value)}
      onSearch={onSearch}
    />
    ```

- **GraphQL Queries**:
  - `SEARCH_EMISSIONS_FACTORS_QUERY` - For global emissions factors
    ```graphql
    query SearchEmissionsFactors($activityName: String!) {
      searchEmissionsFactors(activityName: $activityName) {
        activityName
        referenceProduct
        geography
        source
        unit
      }
    }
    ```

  - `PREDICT_EMISSIONS_FACTORS_QUERY` - For AI-assisted matching
    ```graphql
    query predictEmissionsFactorsQuery(
      $chemicalName: String!
      $productCategory: String
      $casNo: String
      $geography: String
      $geographyModeling: Boolean
      $unit: String
    ) {
      predictEmissionsFactors(
        chemicalName: $chemicalName
        productCategory: $productCategory
        casNo: $casNo
        geography: $geography
        geographyModeling: $geographyModeling
        unit: $unit
      ) {
        matchedActivity {
          activityName
          referenceProduct
          productInformation
          similarity
          curated
          geography
          source
          unit
          // Additional fields
        }
        // More fields
      }
    }
    ```

- **Backend Services**:
  - `emissions_factors.ts` - Handles tenant-specific emissions factor searches
    ```typescript
    export const getEmissionsFactors = async ({ activityName, geography, source, unit }) => {
      try {
        const { orgName: tenantID } = context.currentUser.orgMemberInfo
        const response = await axios.get(
          `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}/all`,
          {
            params: {
              activity_name: sanitizeInput(activityName),
              geography: sanitizeInput(geography)?.join(','),
              source: sanitizeInput(source)?.join(','),
              unit: sanitizeInput(unit)?.join(','),
            },
          }
        )
        // Process response...
      }
    }
    ```

  - `search_emissions_factors.ts` - Handles global emissions factor searches
    ```typescript
    export const searchEmissionsFactors = async ({ activityName }) => {
      try {
        const response = await axios.get(
          `${process.env.LCA_API_ENDPOINT}/emissions-factors/`,
          {
            params: {
              activity_name: activityName,
            }
          }
        )
        // Process response...
      }
    }
    ```

  - `predict_emissions_factors.ts` - AI-assisted emissions factor matching
    ```typescript
    export const predictEmissionsFactors = async ({
      chemicalName,
      productCategory,
      casNo,
      geography,
      geographyModeling,
      unit,
      labs = false,
    }) => {
      // Quota checks and headers setup
      // ...
      const response = await retryOnFail(retryCount, () =>
        axios.post(
          `${process.env.LCA_API_ENDPOINT}/raw-materials/activities/recommendations`,
          {
            chemical_name: sanitizeInput(chemicalName),
            product_category: productCategory ? sanitizeInput(productCategory) : null,
            cas_number: sanitizeInput(casNo),
            geography: sanitizeInput(geography),
            geography_modeling: geographyModeling,
            unit: unit,
          },
          { headers: _headers }
        )
      )
      // Process response...
    }
    ```

### 2. Component Extraction from Files

#### Workflow Overview
1. User uploads a document containing component information
2. System extracts component details from the document
3. Components are added to the product model

#### Components and Services Involved

- **GraphQL Query**: `EXTRACT_COMPONENTS_FROM_FILE_QUERY` in `AddProduct.tsx`
  ```graphql
  const EXTRACT_COMPONENTS_FROM_FILE_QUERY = gql`
    query ExtractComponentsFromFileQuery(
      $base64Data: String!
      $contentType: String!
    ) {
      extractComponentsFromFile(
        base64Data: $base64Data
        contentType: $contentType
      ) {
        components {
          componentId
          componentName
          nodes {
            id
            name
            component
            description
            nodeType
            location {
              city
              country
            }
            amount
            quantity
            unit
            scrapRate
            scrapFate
          }
          edges {
            fromNodeId
            toNodeId
          }
        }
        warnings
      }
    }
  `
  ```

- **Backend Service**: `extract_components_from_file.ts`
  ```typescript
  export const extractComponentsFromFile = async ({
    base64Data,
    contentType,
  }) => {
    try {
      const blob = base64ToBlob(base64Data, contentType)
      const formData = new FormData()
      formData.append('file', blob, 'product_info')

      let headers = {
        'Content-Type': 'multipart/form-data',
        'X-Use-Graph-Extraction': 'true',
        'X-Parts-Only': 'true',
      }

      const response = await axios.post(
        `${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/components`,
        formData,
        { headers }
      )
      // Process response...
    }
  }
  ```

### 3. Custom Emissions Factor Creation

#### Workflow Overview
1. User creates a custom emissions factor when no suitable match is found
2. System saves the custom emissions factor to the tenant's database
3. The custom factor can be used in future calculations

#### Components and Services Involved

- **GraphQL Mutation**: `CREATE_EMISSIONS_FACTOR_MUTATION`
  ```graphql
  mutation CreateEmissionsFactor($emissionsFactor: EmissionsFactorInput!) {
    createEmissionsFactor(emissionsFactor: $emissionsFactor) {
      activityName
      activityType
      description
      referenceProduct
      geography
      source
      kgCO2e
      unit
    }
  }
  ```

- **Backend Service**: `create_emissions_factor.ts`
  ```typescript
  export const createEmissionsFactor = async ({ emissionsFactor }) => {
    try {
      const { orgName: tenantID } = context.currentUser.orgMemberInfo
      const response = await axios.post(
        `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}`,
        sanitizeInput(emissionsFactor)
      )
      return {
        activityName: response.data.activity_name,
        activityType: response.data.activity_type,
        description: response.data.activity_description,
        referenceProduct: response.data.reference_product,
        geography: response.data.geography,
        source: response.data.source,
        kgCO2e: response.data.kg_co2e,
        unit: response.data.unit,
      }
    }
  }
  ```

## API Endpoint Flow

### Add Product Flow

The Add Product page is a multi-step form with the following sections:
1. Product Info
2. Materials
3. Manufacturing
4. Transportation
5. Consumer Use
6. End of Life
7. Finalize

#### Data Loading Strategy

The application uses an eager loading strategy for API calls:

- Many API calls are made upfront when the Add Product page loads or when early tabs are completed
- Prediction calls for later tabs (like Consumer Use or Manufacturing) are often triggered based on data entered in the Product Info or Materials tabs
- When navigating between tabs, the UI typically displays already-loaded data from state rather than making new API calls
- This approach improves user experience by reducing waiting time when switching between tabs

Each section involves different API calls to both the LCA API and ML Models services, but these calls may not directly correspond to the currently visible tab.

##### Initial API Calls on Page Load

When the Add Product page first loads, several API calls are made proactively, even before user interaction:

1. **Mapbox Access Token** (`MAPBOX_ACCESS_TOKEN_QUERY`)
   - Loads the Mapbox token for location-based features
   - Used for factory location and supplier origin mapping

2. **Product Categories** (`PRODUCT_CATEGORIES_QUERY`)
   - Loads all available product categories for dropdown selection
   - Used in the Product Info tab

3. **Raw Materials for Ingredients** (`RAW_MATERIALS_QUERY`)
   - Loads ingredient materials data for autocomplete suggestions
   - Used in the Materials tab

4. **Raw Materials for Packaging** (`RAW_MATERIALS_QUERY` with `isPackaging: true`)
   - Loads packaging materials data for autocomplete suggestions
   - Used in the Materials tab for packaging components

5. **Manufacturing Methods** (`MANUFACTURING_METHODS_QUERY`)
   - Loads manufacturing process options
   - Used in the Manufacturing tab

6. **Product List Check** (`PRODUCTS_QUERY`)
   - For trial users, checks product creation quota
   - Controls feature availability

7. **Reference Data Queries**
   - Various reference data needed throughout the form
   - Includes geography codes, units, and other lookup values

These proactive API calls ensure that all necessary data is available immediately when needed, reducing perceived latency as users progress through the form.

### ML Service Endpoints (port 5001)

The following API calls directly connect to the ML Models service:

#### Copilot Feature

The "Use Copilot" button in the UI provides AI-assisted automation for various tasks:

- **Packaging Copilot**: Automatically suggests a complete packaging setup for the product, including appropriate materials, components, weights, and supplier origins
- **UI Indication**: Available as a button labeled "Use Copilot" in the Materials/Packaging section
- **Backend Integration**: Makes multiple ML-powered predictions in sequence to generate a comprehensive packaging recommendation

1. **Predict Manufacturing Processes**
   - **GraphQL Query**: `predictManufacturingProcesses`
   - **Backend Endpoint**: `GET ${process.env.ML_MODELS_ENDPOINT}/api/product-manufacturing/manufacturing-processes/${productCategory}/${productName}`
   - **Purpose**: Predicts manufacturing processes based on product name and category
   - **UI Trigger**: When entering the Manufacturing tab or when product name/category is updated

2. **Extract Components from File**
   - **GraphQL Query**: `extractComponentsFromFile`
   - **Backend Endpoint**: `POST ${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/components`
   - **Purpose**: Extracts component information from uploaded files (like BOM sheets)
   - **UI Trigger**: When uploading a file in the import flow

3. **Extract File**
   - **GraphQL Query**: `extractFile`
   - **Backend Endpoint**: `POST ${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/`
   - **Purpose**: General file extraction for product information
   - **UI Trigger**: When uploading files for product information extraction

### LCA API Prediction Endpoints (port 5005)

The following calls go to the LCA API service but involve prediction functionality:

1. **Predict Emissions Factors**
   - **GraphQL Query**: `predictEmissionsFactors`
   - **Backend Endpoint**: `POST ${process.env.LCA_API_ENDPOINT}/raw-materials/activities/recommendations`
   - **Purpose**: Predicts emissions factors for materials
   - **UI Trigger**: When adding materials in the Materials tab

2. **Predict Ingredient Source**
   - **GraphQL Query**: `predictIngredientSource`
   - **Backend Endpoint**: `GET ${process.env.LCA_API_ENDPOINT}/ingredients/${tenantID}/source-of-procurement`
   - **Purpose**: Predicts the source of procurement for an ingredient
   - **UI Trigger**: When adding materials in the Materials tab

3. **Predict Product Category**
   - **GraphQL Query**: `predictProductCategory`
   - **Backend Endpoint**: `GET ${process.env.LCA_API_ENDPOINT}/product-categories/predict-category`
   - **Purpose**: Predicts product category based on product name
   - **UI Trigger**: When entering product name in the Product Info tab

4. **Predict Consumer Use**
   - **GraphQL Query**: `predictConsumerUse`
   - **Backend Endpoint**: `GET ${process.env.LCA_API_ENDPOINT}/consumer-use/predict-process-model/${geographyIso3}/${productCategory}`
   - **Purpose**: Predicts consumer use patterns and resource consumption
   - **UI Trigger**: Often triggered earlier in the flow when product category is selected
   - **UI Indication**: Resources with AI-assisted predictions are marked with an "AI" badge

   This endpoint predicts consumer use details such as:
   - Resource types needed during product use (e.g., energy_electricity, fresh_water, waste_water)
   - Matched activities for each resource (e.g., "electricity production, coal, aluminium industry")
   - Estimated consumption amounts (e.g., 0.79 kWh of electricity, 50 kg of water)
   - Appropriate units for each resource
   - Geography-specific customizations (e.g., "customized for US")

   When the user clicks "Save" in the Consumer Use tab, the application:
   1. Creates/updates emissions factors for any modified resources
   2. Updates the process model with all consumer use nodes
   3. Connects these nodes in the proper sequence within the overall product lifecycle

5. **Predict Product Packaging**
   - **GraphQL Query**: `predictProductPackaging`
   - **Backend Endpoint**: `POST ${process.env.LCA_API_ENDPOINT}/product-packaging/packaging-predictions`
   - **Purpose**: Predicts packaging materials and weights
   - **UI Trigger**: When entering the Materials tab for packaging information
   - **UI Indication**: Materials with AI-assisted predictions are marked with an "AI" badge in the UI

   This endpoint is particularly important in the packaging section, where it predicts:
   - Appropriate packaging materials (e.g., PAPER, PET, PP, CARDBOARD)
   - Component types (e.g., Label, Bottle, Screw Cap, Outer Case)
   - Weight estimates for each component
   - Packaging tier classification (Primary, Secondary)
   - Supplier origin predictions (e.g., Sweden, China, Germany)

### Complete API Flow for Add Product

The full endpoint flow for creating a product is:

1. **Initial Product Creation**:
   - `createProduct` mutation → `POST ${process.env.LCA_API_ENDPOINT}/products/${tenantID}`

2. **Adding Materials and Packaging**:
   - `getRawMaterials` query → `GET ${process.env.LCA_API_ENDPOINT}/raw-materials`
   - `createSupplier` mutation → `POST ${process.env.LCA_API_ENDPOINT}/suppliers/${tenantID}`
   - `createEmissionsFactor` mutation (if needed)
   - `predictIngredientSource` query → `GET ${process.env.LCA_API_ENDPOINT}/ingredients/${tenantID}/source-of-procurement`

3. **Adding Manufacturing Processes**:
   - `getManufacturingMethods` query → `GET ${process.env.LCA_API_ENDPOINT}/manufacturing-processes/`
   - `createProductManufacturing` mutation
   - `predictManufacturingProcesses` query → `GET ${process.env.ML_MODELS_ENDPOINT}/api/product-manufacturing/manufacturing-processes/...`

4. **Creating Process Model**:
   - `createProductProcessModel` mutation → `POST ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/process-model`

5. **Activating the Product**:
   - `activateProduct` mutation → `POST ${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${productId}/activate`

### Save Process in Add Product Flow

When clicking "Save" in any tab of the Add Product flow (such as Consumer Use), the following process occurs:

1. **Backup Current State** (if in edit mode):
   - `backupProduct` mutation → `PUT ${process.env.LCA_API_ENDPOINT}/products/${tenantID}/backup-product/${productId}`
   - Creates a backup of the current product state before making changes

2. **Create/Update Emissions Factors** (if modified):
   - `createEmissionsFactor` mutation → `POST ${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}`
   - For each resource with modified emissions factors, creates or updates the emissions factor

3. **Update Process Model**:
   - `createProductProcessModel` mutation → `POST ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/process-model`
   - Sends the complete process model with all nodes (including the newly added/modified ones) and edges
   - This is a comprehensive update that includes all lifecycle stages, not just the current tab

4. **Error Handling**:
   - If any step fails, the system attempts to restore the previous state
   - `deleteProduct` mutation may be called to clean up if a new product creation fails
   - Error notifications are displayed to the user

## Technical Notes

### Emissions Factor Search
- The system uses MySQL full-text search in BOOLEAN MODE for emissions factor searches
- Special characters in search terms (e.g., hyphens in "Metal turning CNC controlled manufacturing - per kg removed") require special handling
- Search terms are often extracted from uploaded documents and may contain various special characters
- The `sanitizeInput()` function is used in some API calls but not consistently across all search endpoints

### File Processing
- The system supports multiple file formats: PDF, Word, Excel, CSV, TSV, HTML, ODT
- Files are converted to base64 for transmission to the backend
- The ML Models service extracts structured data from unstructured documents
- Process model extraction can be enabled/disabled via organization metadata

### AI-Assisted Features
- AI-assisted predictions are visually indicated with an "AI" badge in the UI
- These badges appear next to materials, ingredients, and other elements where ML predictions have been applied
- The system uses a combination of ML models to predict:
  - Material properties and emissions factors
  - Supplier origins and locations
  - Packaging components and weights
  - Manufacturing processes
- The "Use Copilot" feature provides more comprehensive AI assistance for complex tasks

### Product Detail View

The Product Detail page displays comprehensive information about a product after it has been created and activated. It features multiple tabs that provide different views of the product data:

1. **Impact Summary Tab**:
   - Displays total carbon footprint (e.g., "1.1262 kg CO2e")
   - Shows category benchmark comparison (e.g., "much better than average")
   - Presents a breakdown of emissions by lifecycle stage with percentages
   - Includes a visual pie chart representation of emissions distribution

2. **Product Details Tab**:
   - Shows basic product information (SKU, brand, category, etc.)
   - Displays product attributes and metadata

3. **Lifecycle Inventory Data Tab**:
   - Provides detailed tables of materials, manufacturing processes, transportation, consumer use, and end-of-life data
   - Allows editing of individual components with "Save Changes" functionality

4. **Process Model Tab**:
   - Renders an interactive visual graph of the product lifecycle
   - Shows nodes for each stage and connections between them
   - Allows editing the process model structure

5. **Report Tab**:
   - Provides options for generating standardized LCA reports

#### Data Loading Strategy for Product Detail View

The Product Detail view uses an efficient single-load approach:

1. **Initial Comprehensive Data Load**:
   - When the Product Detail page loads, a single GraphQL query (`ProductInfoQuery`) fetches ALL data for ALL tabs at once
   - Backend endpoint: `GET ${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${productId}`
   - Process model data: `GET ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/process-model`
   - Emissions calculations: `GET ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/process-model/walk`

2. **Tab Navigation Without API Calls**:
   - Switching between tabs does NOT trigger additional API calls
   - Each tab simply renders different views of the already-loaded data
   - This provides a smooth, responsive user experience

3. **Emissions Calculation**:
   - Emissions are calculated server-side using the `calculateSegmentEmissions` function
   - Each lifecycle stage's emissions are summed to produce the total product footprint
   - Results are returned as part of the initial data load

4. **Additional API Calls Only When Editing**:
   - API calls are only made when explicitly editing data (e.g., in Lifecycle Inventory Data tab)
   - When saving changes: `PUT ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/nodes`
   - When updating the process model: `POST ${process.env.LCA_API_ENDPOINT}/v2/products/${tenantID}/${productId}/process-model`

### Authentication and Authorization
- PropelAuth is used for authentication
- Organization-based multi-tenancy is implemented
- User quotas for certain features (e.g., emissions factor searches) are enforced

### Environment Configuration
- External service endpoints are configured via environment variables:
  - `LCA_API_ENDPOINT` - LCA calculation service (port 5005)
  - `ML_MODELS_ENDPOINT` - Machine learning models for document extraction (port 5001)
  - `MAPBOX_SECRET_TOKEN` - Mapbox integration for geographical visualization