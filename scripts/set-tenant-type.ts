#!/usr/bin/env node

/**
 * <PERSON>ript to set tenant type for testing non-carbon tenant functionality
 * 
 * Usage:
 * yarn rw exec set-tenant-type
 */

import { UpdateOrgMetadata } from 'src/lib/auth'

export default async () => {
  try {
    // You'll need to replace this with your actual org ID
    // You can find this in your PropelAuth dashboard or by logging in and checking the JWT
    const orgId = 'YOUR_ORG_ID_HERE'
    
    console.log('Setting tenant type to "non-carbon"...')
    
    const response = await UpdateOrgMetadata(orgId, 'tenantType', 'non-carbon')
    
    console.log('✅ Successfully set tenant type to "non-carbon"')
    console.log('Response:', response)
    
    console.log('\nNow you can test the manufacturing method prediction:')
    console.log('1. Go to Add Product page')
    console.log('2. Set category to "Laundry Detergent"')
    console.log('3. Go to Manufacturing step')
    console.log('4. Click "Use Copilot"')
    console.log('5. Should predict "Laundry Detergent manufacturing"')
    
  } catch (error) {
    console.error('❌ Error setting tenant type:', error)
  }
}
