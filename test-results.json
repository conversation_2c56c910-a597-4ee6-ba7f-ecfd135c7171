{"config": {"configFile": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/playwright.config.ts", "rootDir": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results.json"}], ["list", {"printSteps": true}]], "reportSlowTests": {"max": 5, "threshold": 15000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/test-results", "repeatEach": 1, "retries": 0, "id": "setup", "name": "setup", "testDir": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests", "testIgnore": [], "testMatch": ["/.*\\.setup\\.ts/"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/test-results", "repeatEach": 1, "retries": 0, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "version": "1.42.1", "workers": 1, "webServer": {"reuseExistingServer": true, "command": "yarn rw dev", "port": 8910}}, "suites": [{"title": "auth.setup.ts", "file": "auth.setup.ts", "column": 0, "line": 0, "specs": [{"title": "setup authentication", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "setup", "projectName": "setup", "results": [{"workerIndex": 0, "status": "timedOut", "duration": 60001, "error": {"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests/auth.setup.ts", "column": 5, "line": 20}, "message": "Error: browserContext.close: Test ended.\nBrowser logs:\n\n<launching> /Users/<USER>/Library/Caches/ms-playwright/chromium-1105/chrome-mac/Chromium.app/Contents/MacOS/Chromium --disable-field-trial-config --disable-background-networking --enable-features=NetworkService,NetworkServiceInProcess --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=ImprovedCookieControls,LazyFrameLoading,GlobalMediaControls,DestroyProfileOnBrowserClose,MediaRouter,DialMediaRouteProvider,AcceptCH<PERSON>rame,AutoExpandDetailsElement,CertificateTransparencyComponentUpdater,AvoidUnnecessaryBeforeUnloadCheckSync,Translate,HttpsUpgrades,PaintHolding --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --enable-use-zoom-for-dsf=false --use-angle --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=/var/folders/4k/0r8qqlmn4sbg7r3n55_yr5200000gn/T/playwright_chromiumdev_profile-4iSvA3 --remote-debugging-pipe --no-startup-window\n<launched> pid=13583\n[pid=13583][err] [0602/091559.708914:WARNING:runtime_features.cc(733)] AttributionReportingCrossAppWeb cannot be enabled in this configuration. Use --enable-features=ConversionMeasurement,AttributionReportingCrossAppWeb in addition.\n[pid=13583][err] [0602/091602.716311:WARNING:runtime_features.cc(733)] AttributionReportingCrossAppWeb cannot be enabled in this configuration. Use --enable-features=ConversionMeasurement,AttributionReportingCrossAppWeb in addition.\n[pid=13583][err] [0602/091605.632674:WARNING:runtime_features.cc(733)] AttributionReportingCrossAppWeb cannot be enabled in this configuration. Use --enable-features=ConversionMeasurement,AttributionReportingCrossAppWeb in addition.\n[pid=13583] <gracefully close start>\n\n\u001b[0m \u001b[90m 18 |\u001b[39m     \u001b[36mthrow\u001b[39m error\u001b[33m;\u001b[39m\u001b[0m\n\u001b[0m \u001b[90m 19 |\u001b[39m   } \u001b[36mfinally\u001b[39m {\u001b[0m\n\u001b[0m\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m context\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[0m \u001b[90m    |\u001b[39m     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[0m\n\u001b[0m \u001b[90m 21 |\u001b[39m   }\u001b[0m\n\u001b[0m \u001b[90m 22 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[0m \u001b[90m 23 |\u001b[39m\u001b[0m\n\n\u001b[2m    at /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/tests/auth.setup.ts:20:5\u001b[22m"}], "stdout": [], "stderr": [{"text": "❌ Error during authentication: page.waitForSelector: Test ended.\nCall log:\n  \u001b[2m- waiting for locator('text=Welcome!') to be visible\u001b[22m\n\n    at login \u001b[90m(/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/\u001b[39mtests/utils.ts:13:14\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/\u001b[39mtests/auth.setup.ts:11:5\n"}], "retry": 0, "startTime": "2025-06-02T03:45:59.082Z", "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/test-results/auth.setup.ts-setup-authentication-setup/test-failed-1.png"}, {"name": "trace", "contentType": "application/zip", "path": "/Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/test-results/auth.setup.ts-setup-authentication-setup/trace.zip"}]}], "status": "unexpected"}], "id": "17e3fe6f4d9d8bd79c6b-3bfb286eeeeaebb9a787", "file": "auth.setup.ts", "line": 4, "column": 5}]}, {"title": "e2e/02-01-inventory-add-product.spec.ts", "file": "e2e/02-01-inventory-add-product.spec.ts", "column": 0, "line": 0, "specs": [{"title": "Add Product", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9863c5e03c8f2464cfa3-5b2e703b1f08eaeb3792", "file": "e2e/02-01-inventory-add-product.spec.ts", "line": 21, "column": 5}]}], "errors": [], "stats": {"startTime": "2025-06-02T03:45:58.857Z", "duration": 61066.761, "expected": 0, "skipped": 1, "unexpected": 1, "flaky": 0}}