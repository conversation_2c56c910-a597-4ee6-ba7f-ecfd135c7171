# LCA Lifecycle Stage Implementation Workflow

## Overview
This document details the implementation of the `lca_lifecycle_stage` parameter for the emissions factor matching endpoint across the CarbonBright web application.

## 1. Four Phases Where Endpoint is Called

### 1.1 LabEFMatchingComponent 🔴 (Not Implemented Yet)
- **Location**: `/lab-ef-matching` page
- **Component**: `web/src/components/LabEFMatchingComponent/LabEFMatchingComponent.tsx`
- **Status**: Pending implementation (Phase 2)
- **Context**: Standalone lab testing environment with no inherent lifecycle context

### 1.2 AddProduct Component ✅ (Implemented)
- **Location**: `/product/add`, `/product/import`, `/product/{id}/edit/{step}`
- **Component**: `web/src/components/AddProduct/AddProduct.tsx`
- **Status**: Implemented
- **Context**: Product creation/editing with clear step-based lifecycle stages

### 1.3 GraphModel Component ✅ (Implemented)  
- **Location**: Product detail pages via `ProductInfoCell`
- **Component**: `web/src/components/GraphModel/GraphModel.tsx`
- **Status**: Implemented
- **Context**: Interactive product lifecycle graph with node-based stages

### 1.4 EmissionsFactorSelector Component ✅ (Implemented)
- **Location**: Used across multiple components
- **Component**: `web/src/components/EmissionsFactorSelector/EmissionsFactorSelector.tsx`
- **Status**: Implemented
- **Context**: Inherits lifecycle context from parent components

## 2. Changes Made

### 2.1 GraphQL Schema & API Enhancement

#### Frontend GraphQL Query
**File**: `web/src/utils/graphql.ts`

**Added Parameter**:
```graphql
$lcaLifecycleStage: String
```

**Added to Query Variables**:
```graphql
lcaLifecycleStage: $lcaLifecycleStage
```

#### Backend GraphQL Schema  
**File**: `api/src/graphql/predict_emissions_factors.sdl.ts`

**Added Parameter to Schema**:
```graphql
type Query {
  predictEmissionsFactors(
    chemicalName: String!
    productCategory: String
    casNo: String
    geography: String
    geographyModeling: Boolean
    unit: String
    labs: Boolean
    lcaLifecycleStage: String  # ← NEW parameter added
  ): ActivityRecommendations @requireAuth
}
```

#### Backend Service Implementation
**File**: `api/src/services/product/predict_emissions_factors.ts`

**Updated Function Signature**:
- Added `lcaLifecycleStage` parameter to function
- Added `lca_lifecycle_stage: lcaLifecycleStage` to API request payload

### 2.2 AddProduct Component Changes
**File**: `web/src/components/AddProduct/AddProduct.tsx`

**Added Lifecycle Stage Mapping Function**:
```javascript
const mapStepToLifecycleStage = (step, activityType) => {
  const stepToStageMap = {
    0: 'Product Development',  // Product Info
    1: activityType === 'packaging' ? 'Packaging' : 'Raw Materials',
    2: 'Manufacturing',        // Manufacturing
    3: 'Transportation',       // Transportation
    4: 'Use Phase',           // Consumer Use
    5: 'End-of-Life',         // End of Life
  }
  return stepToStageMap[step] || null
}
```

**Updated Function Signature**:
- Added `lcaLifecycleStage = null` parameter to `handlePredictEmissionsFactors`
- Pass lifecycle stage in API calls based on `currentStep`

**Special Handling**:
- Regular materials: Uses current step mapping
- Recycled content: Always uses "End-of-Life"

### 2.3 GraphModel Component Changes
**File**: `web/src/components/GraphModel/GraphModel.tsx`

**Added Node Type Mapping Function**:
```javascript
const mapNodeTypeToLifecycleStage = (nodeType) => {
  const nodeTypeToStageMap = {
    'material': 'Raw Materials',
    'packaging': 'Packaging',
    'production': 'Manufacturing',
    'transportation': 'Transportation',
    'use': 'Use Phase',
    'eol': 'End-of-Life',
    'bundle': 'Manufacturing',
  }
  return nodeTypeToStageMap[nodeType] || null
}
```

**Updated API Call**:
- Pass `lcaLifecycleStage: mapNodeTypeToLifecycleStage(nodeType)` in emissions factor predictions

### 2.4 EmissionsFactorSelector Component Changes
**File**: `web/src/components/EmissionsFactorSelector/EmissionsFactorSelector.tsx`

**Added Activity Type Mapping Function**:
```javascript
const mapActivityTypeToLifecycleStage = (activityType) => {
  const activityTypeToStageMap = {
    'material': 'Raw Materials',
    'Raw Materials': 'Raw Materials',
    'packaging': 'Packaging',
    'production': 'Manufacturing',
    'Total Carbon Emissions': 'Manufacturing',
    'Electricity usage': 'Manufacturing',
    'Water': 'Manufacturing',
    'transportation': 'Transportation',
    'use': 'Use Phase',
    'consumerUse': 'Use Phase',
    'eol': 'End-of-Life',
  }
  return activityTypeToStageMap[activityType] || null
}
```

**Updated API Call**:
- Pass `lcaLifecycleStage: mapActivityTypeToLifecycleStage(activityType)` in emissions factor search

## 3. Associated User Journeys

### 3.1 Product Creation Journey (AddProduct)
**User Flow**:
1. User navigates to "Add Product" (`/product/add`)
2. User goes through step-by-step product creation:
   - **Step 0**: Product Information → `lcaLifecycleStage: "Product Development"`
   - **Step 1**: Raw Materials/Packaging → `lcaLifecycleStage: "Raw Materials"` or `"Packaging"`
   - **Step 2**: Manufacturing → `lcaLifecycleStage: "Manufacturing"`
   - **Step 3**: Transportation → `lcaLifecycleStage: "Transportation"`
   - **Step 4**: Consumer Use → `lcaLifecycleStage: "Use Phase"`
   - **Step 5**: End of Life → `lcaLifecycleStage: "End-of-Life"`

**Trigger Points**:
- User adds/edits material ingredients
- User searches for emissions factors
- System auto-predicts emissions factors for materials
- Special case: Recycled content always maps to "End-of-Life"

### 3.2 Product Detail Interaction Journey (GraphModel)
**User Flow**:
1. User navigates to product detail page (`/{tenantID}/product-detail/{productId}`)
2. User clicks on "Lifecycle Inventory Data" tab
3. User sees the product lifecycle graph with nodes
4. User clicks edit button (🖊️) on any node to modify emissions factors

**Node-Specific Mappings**:
- **Material Node**: User edits raw material → `lcaLifecycleStage: "Raw Materials"`
- **Packaging Node**: User edits packaging → `lcaLifecycleStage: "Packaging"`
- **Production Node**: User edits manufacturing → `lcaLifecycleStage: "Manufacturing"`
- **Transportation Node**: User edits transport → `lcaLifecycleStage: "Transportation"`
- **Use Node**: User edits use phase → `lcaLifecycleStage: "Use Phase"`
- **EOL Node**: User edits end-of-life → `lcaLifecycleStage: "End-of-Life"`

### 3.3 Emissions Factor Selection Journey (EmissionsFactorSelector)
**User Flow**:
1. User triggers emissions factor selection from any parent component
2. Modal/drawer opens with search functionality
3. User searches for alternative emissions factors
4. System passes appropriate lifecycle stage based on context

**Context Inheritance**:
- **From AddProduct**: Inherits step-based lifecycle stage
- **From GraphModel**: Inherits node-type-based lifecycle stage  
- **From ProductInfoCell**: Inherits activity-type-based lifecycle stage

### 3.4 Product Editing Journey
**User Flow**:
1. User navigates to edit product (`/product/{productId}/edit/{step}`)
2. User can jump to specific steps or proceed sequentially
3. Each step maintains lifecycle stage context
4. Emissions factor searches are contextualized to current step

## 4. Backend Endpoint & Parameter Details

### 4.1 Endpoint Information
**GraphQL Resolver**: `predictEmissionsFactors`  
**REST API Endpoint**: `/api/emissions-factor-matching/activities/recommendations`  
**GraphQL Operation**: `predictEmissionsFactorsQuery`

### 4.2 Complete Parameter Reference

#### Required Parameters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `chemicalName` | `String!` | Name of the chemical/material to match | `"Polymeric Material (Polyurethane (PU))"` |

#### Optional Parameters
| Parameter | Type | Description | Example | Default |
|-----------|------|-------------|---------|---------|
| `productCategory` | `String` | Category of the product | `"Office Chairs"` | `null` |
| `casNo` | `String` | Chemical Abstract Service number | `"9002-84-0"` | `null` |
| `geography` | `String` | Geographic region code | `"GLO"`, `"USA"`, `"TWN"`, `"RER"` | `"GLO"` |
| `geographyModeling` | `Boolean` | Enable geography-based modeling | `true`, `false` | `false` |
| `unit` | `String` | Unit of measurement | `"kg"`, `"g"`, `"ton"`, `"kilometers"` | `null` |
| `lcaLifecycleStage` | `String` | **NEW** - LCA lifecycle stage context | `"Raw Materials"`, `"Manufacturing"` | `null` |

#### Lab-Specific Parameter (LabEFMatchingComponent only)
| Parameter | Type | Description | Example | Used In |
|-----------|------|-------------|---------|---------|
| `labs` | `Boolean` | Lab environment flag | `true` | LabEFMatchingComponent only |

### 4.3 Parameter Usage by Component

#### AddProduct Component
- ✅ `chemicalName` (ingredient name)
- ✅ `productCategory` (from form)
- ✅ `casNo` (from ingredient data)
- ✅ `geography` (from supplier location)
- ✅ `geographyModeling: true`
- ✅ `unit: "g"`
- ✅ `lcaLifecycleStage` (mapped from currentStep)

#### GraphModel Component
- ✅ `chemicalName` (node name)
- ❌ `productCategory: null`
- ❌ `casNo: null`
- ✅ `geography` (from node location)
- ✅ `geographyModeling: true`
- ✅ `unit` (from node data)
- ✅ `lcaLifecycleStage` (mapped from nodeType)

#### EmissionsFactorSelector Component
- ✅ `chemicalName` (from form input)
- ✅ `productCategory` (if enabled)
- ✅ `casNo` (from selectedItem)
- ✅ `geography` (from form)
- ✅ `geographyModeling` (configurable)
- ✅ `unit` (from selectedItem)
- ✅ `lcaLifecycleStage` (mapped from activityType)

#### LabEFMatchingComponent (Not Updated)
- ✅ `chemicalName` (search input)
- ❌ `productCategory: null`
- ❌ `casNo: null`
- ✅ `geography: "GLO"`
- ❌ `geographyModeling: false`
- ❌ `unit: null`
- ✅ `labs: true` (unique to lab)
- ❌ `lcaLifecycleStage: null` (pending implementation)

### 4.4 API Request Structure Examples

**Standard API Call**:
```javascript
{
  variables: {
    chemicalName: "Polymeric Material (Polyurethane (PU))",  // Required
    productCategory: null,                                   // Optional
    casNo: null,                                            // Optional
    geography: "GLO",                                       // Required
    geographyModeling: true,                                // Optional
    unit: "kg",                                             // Optional
    lcaLifecycleStage: "Raw Materials"                      // NEW - Optional
  }
}
```

### 4.5 Lifecycle Stage Values Sent
**Possible Values**:
- `"Product Development"` - During initial product setup
- `"Raw Materials"` - For material/ingredient nodes
- `"Packaging"` - For packaging material nodes
- `"Manufacturing"` - For production/manufacturing nodes
- `"Transportation"` - For transport/logistics nodes
- `"Use Phase"` - For consumer use nodes
- `"End-of-Life"` - For disposal/recycling nodes
- `null` - When no context is available (Lab scenario)

### 4.6 Real Production Examples

**Material Node Edit** (from Product Detail → Lifecycle Inventory Data):
```json
{
  "operationName": "predictEmissionsFactorsQuery",
  "variables": {
    "chemicalName": "Polymeric Material (Polyurethane (PU))",
    "casNo": null,
    "geography": "GLO",
    "geographyModeling": true,
    "lcaLifecycleStage": "Raw Materials",
    "unit": "kg"
  }
}
```

**Transportation Node Edit** (from Product Detail → Process Model):
```json
{
  "operationName": "predictEmissionsFactorsQuery",
  "variables": {
    "chemicalName": "Polymeric Material (Nylon Glass Filled) bundle Transportation Segment 3",
    "casNo": null,
    "geography": "GLO", 
    "geographyModeling": true,
    "lcaLifecycleStage": "Transportation",
    "unit": "kilometers"
  }
}
```

**Production Node Edit** (from Product Detail → Process Model):
```json
{
  "operationName": "predictEmissionsFactorsQuery",
  "variables": {
    "chemicalName": "Injection Molded",
    "casNo": null,
    "geography": "USA",
    "geographyModeling": true,
    "lcaLifecycleStage": "Manufacturing",
    "unit": "g"
  }
}
```

## 5. Implementation Status

### ✅ Completed (Phase 1)
- [x] Frontend GraphQL query parameter addition
- [x] Backend GraphQL schema parameter addition
- [x] Backend service implementation  
- [x] AddProduct component lifecycle stage mapping
- [x] GraphModel component node type mapping  
- [x] EmissionsFactorSelector activity type mapping
- [x] Fixed hardcoded activityType in GraphModel handleEmissionsFactorEdit
- [x] API calls passing lifecycle stage context
- [x] Full end-to-end implementation working

### 🔄 Pending (Phase 2)
- [ ] LabEFMatchingComponent implementation
- [ ] UI dropdown for manual lifecycle stage selection in Lab
- [ ] Default handling for Lab scenario

## 6. Benefits

### 6.1 For Users
- More contextually relevant emissions factor recommendations
- Improved accuracy of environmental impact calculations
- Better user experience with stage-appropriate suggestions

### 6.2 For Backend
- Enhanced filtering and matching capabilities
- Better algorithm performance with additional context
- More targeted database queries
- Improved recommendation relevance

## 7. Testing Verification

**Confirmed Working**:
- Product detail page material editing shows `lcaLifecycleStage: "Raw Materials"`
- API requests include the new parameter
- No breaking changes to existing functionality
- All diagnostics pass without errors

**Example Verification**:
```
URL: localhost:8910/humanscale/product-detail/1748087548958
Action: Edit material node emissions factor
Result: API call includes "lcaLifecycleStage": "Raw Materials"
```
