# Understanding the CarbonBright Web App

This document aims to capture a high-level understanding of the CarbonBright Web App, its purpose, and key user interactions. It will be updated as more details are uncovered.

## 1. Project Purpose (The "WHY")

The CarbonBright Web App is designed for **Life Cycle Assessment (LCA)** and **carbon footprint management**. Its primary goals are to:

*   **Calculate and Track Carbon Footprints:** Enable users to model products across their lifecycle (raw materials, manufacturing, packaging, distribution, consumer use, end-of-life) and calculate their environmental impact, focusing on carbon footprint (e.g., GWP100).
*   **Manage Product and Component Data:** Allow users to create, import, and manage an inventory of products and their constituent components.
*   **Analyze Supply Chain Emissions:** Help businesses understand and track emissions from their supply chain, particularly Scope 3 emissions.
*   **Utilize Emissions Factor Databases and Tools:** Integrate with or use emissions factor databases (e.g., Ecoinvent, DEFRA) and potentially AI-driven tools to link activities/materials to environmental impact data.
*   **Provide Reporting and Insights:** Generate reports and visualizations to help users understand their environmental performance and make data-driven sustainability decisions.

In essence, the CarbonBright Web App aims to be a comprehensive tool for businesses to measure, understand, manage, and report on the environmental impact (primarily carbon footprint) of their products throughout their entire lifecycle.

## 2. Key Features (High-Level)

*   Product Lifecycle Modeling
*   Carbon Footprint Calculation
*   Product & Component Inventory Management
*   Supplier Emissions Tracking
*   Emissions Factor Database Integration
*   AI-assisted LCA and Emission Factor Matching
*   Reporting and Data Visualization (Dashboards, Charts, Maps)
*   User Onboarding & Guided Tours
*   Predictive Analytics (e.g., consumer use patterns)
*   User Authentication and Account Management (tiered access)
*   Product Comparison: Allows users to compare emissions and attributes of different products (see <mcfile name="CompareProducts.tsx" path="web\src\components\CompareProducts\CompareProducts.tsx"></mcfile>).
*   Sustainability Credentials Display: Showcases product sustainability ratings, claims, and verification details (see <mcfile name="ProductSustainabilityWidget.tsx" path="web\src\components\ProductSustainabilityWidget\ProductSustainabilityWidget.tsx"></mcfile>).
*   Integration with Product Category Rules (PCRs) for standardized assessments.

## 3. Technology Stack (Preliminary)

*   Framework: RedwoodJS
*   API: GraphQL
*   Database ORM: Prisma
*   Authentication: PropelAuth
*   Frontend UI Components: Ant Design (e.g., `antd` Tour, Checkbox, Button components as seen in <mcfile name="GettingStartedTour.tsx" path="web\src\components\GettingStartedTour\GettingStartedTour.tsx"></mcfile>)
*   Charting/Visualization: Potentially `@ant-design/charts`
*   Mapping Services: Mapbox for geographical data visualization (used in <mcfile name="ProductInfoCell.tsx" path="web\src\components\ProductInfoCell\ProductInfoCell.tsx"></mcfile> and <mcfile name="DashboardCell.tsx" path="web\src\components\DashboardCell\DashboardCell.tsx"></mcfile>).
*   Error Tracking: Sentry (indicated by `SENTRY_DSN` in `redwood.toml`).
*   External APIs: Leverages external services for core functionalities, such as an LCA API (`LCA_API_ENDPOINT`) and Machine Learning models (`ML_MODELS_ENDPOINT`).

## 4. Potential User Journeys

### Journey 1: New User Onboarding & Initial Interaction
1.  User signs up/logs in.
2.  User is presented with a "Getting Started Tour" (<mcfile name="GettingStartedTour.tsx" path="web\src\components\GettingStartedTour\GettingStartedTour.tsx"></mcfile>).
    *   Tour highlights: creating a product, viewing insights, designing sustainable products, sharing reports.
    *   Option to "Don't show this again" which logs tour completion status (<mcsymbol name="LOG_PRODUCT_TOUR_STATUS" filename="GettingStartedTour.tsx" path="web\src\components\GettingStartedTour\GettingStartedTour.tsx" startline="5" type="variable"></mcsymbol>).
3.  User sees a "Getting Started" checklist (<mcfile name="GettingStartedCell.tsx" path="web\src\components\GettingStartedCell\GettingStartedCell.tsx"></mcfile>):
    *   "Create My First Product"
    *   "Download Product Impact Report"
    *   Potentially "Upgrade To Full Version"

### Journey 2: Product Creation and Management
1.  User navigates to the products section (e.g., via `routes.products()` after tour completion).
2.  User views their list of existing products (<mcfile name="ProductsCell.tsx" path="web\src\components\ProductsCell\ProductsCell.tsx"></mcfile>).
3.  User creates a new product.
    *   Enters basic product details.
    *   This action is handled by the `api/src/services/product/create_product.ts` service, which makes a `POST` request to an external LCA API endpoint: `/products/{tenant_id}`.
    *   Guided by an "AI-based LCA Co-pilot" (as mentioned in the tour).
    *   The creation event is also logged internally (e.g., via <mcfile name="log_create_product.ts" path="api\src\services\product\log_create_product.ts"></mcfile>).
4.  User defines product components, materials, manufacturing processes, packaging, etc.
5.  User defines transport segments for the product (<mcfile name="create_product_transport.ts" path="api\src\services\product\create_product_transport.ts"></mcfile>).
6.  User manages product details (e.g., adds tags).
7.  User exports product inventory data (e.g., to CSV).

### Journey 3: Viewing Dashboards and Analyzing Product Impact
1.  User navigates to the main dashboard (<mcfile name="DashboardCell.tsx" path="web\src\components\DashboardCell\DashboardCell.tsx"></mcfile>).
2.  User views key metrics:
    *   Overall carbon footprint.
    *   Emissions breakdown by life cycle stage.
    *   Emissions by product category.
    *   Highest/lowest footprint products.
    *   Geographical impact.
3.  User interacts with charts and maps for deeper analysis.

### Journey 4: Generating and Accessing Reports
1.  User navigates to the "Reports" section (<mcfile name="ReportsPage.tsx" path="web\src\pages\ReportsPage\ReportsPage.tsx"></mcfile>).
2.  User views available summary reports:
    *   Product Inventory Summary.
    *   Product Emissions by Category.
    *   Supplier Emissions Summary.
3.  User attempts to access an advanced report and might be prompted to upgrade if on a trial.
4.  User downloads a detailed LCA report for a specific product (<mcfile name="downloadLcaReport.ts" path="api\src\functions\downloadLcaReport\downloadLcaReport.ts"></mcfile>).
    *   This action is logged as a milestone (<mcfile name="log_report_download.ts" path="api\src\services\product\log_report_download.ts"></mcfile>).

### Journey 5: Utilizing Predictive Features
1.  While defining a product, user reaches the "consumer use" phase.
2.  System predicts consumer use patterns based on product category and geography (<mcfile name="predict_consumer_use.ts" path="api\src\services\product\predict_consumer_use.ts"></mcfile>).
3.  User reviews and potentially adjusts these predictions.

### Journey 6: Seeking Help and Support
1.  User accesses various help and support features through a dedicated interface (primarily <mcfile name="HelpAndSupport.tsx" path="web\src\components\HelpAndSupport\HelpAndSupport.tsx"></mcfile>).
2.  User can:
    *   Retake the Product Tour.
    *   Access the Help Center (knowledge base articles, tutorials).
    *   Contact support via a form (with optional screenshot functionality).
    *   Submit feedback (with optional screenshot functionality).
    *   View "About" information and "Terms of Service".
3.  User opts to retake the product tour.
4.  User contacts support or submits feedback.

### Journey 7: Authentication and Account Management
1.  User signs up or logs in.
2.  System authenticates the user (<mcfile name="auth.ts" path="api\src\lib\auth.ts"></mcfile>).
3.  User's access is determined by their organization and subscription tier.
4.  If no organization is associated, user might be directed to a "lab" or sandbox environment (<mcfile name="MainLayout.tsx" path="web\src\layouts\MainLayout\MainLayout.tsx"></mcfile>).

---

*This document is a work in progress and will be updated as more information is gathered.*
```
