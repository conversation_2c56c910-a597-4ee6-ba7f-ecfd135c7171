# Implementation Plan for Manufacturing UI Changes

## Requirements

1. Add scrap rate and scrap fate options to manufacturing method
2. Modify manufacturing method prediction for non-carbon tenants

## Requirement 1: Add Scrap Rate and Scrap Fate Options

### Premise
Manufacturing methods need to include two new fields:
1. Scrap Rate (as a percentage)
2. Scrap Fate (with options: landfill, recycling, incineration, composting)

### Required Steps
1. **UI Changes**
   - Location: `web/src/components/AddProduct/AddProduct.tsx`
   - Add new form fields in the manufacturing method section:
     - Add Scrap Rate input field (type: number, with % symbol)
     - Add Scrap Fate dropdown with options:
       - Landfill
       - Recycling
       - Incineration
       - Composting

2. **Data Model**
   - Verified that existing GraphQL schema already supports these fields:
     - `scrap_rate: Float`
     - `scrap_fate: String`
   - No API contract changes needed

3. **Testing Requirements**
   - Verify new fields appear in manufacturing method form
   - Test input validation for scrap rate (valid percentage)
   - Test selection of all scrap fate options
   - Verify data persistence
   - Test edge cases (null values, invalid inputs)

## Requirement 2: Modify Manufacturing Method Prediction

### Premise
The system currently predicts manufacturing methods for products using a hardcoded mapping and a fallback default value:
```typescript
const preferredManufacturingActivityName = {
  'Shopping Totes': 'Garment Cut and Sew',
  default: 'Cleaning Products Manufacturing',
}
```

However, for non-carbon tenants, the requirement is to use a different naming convention:
`[category name] + " manufacturing"`

For example, if the product category is "Laundry Detergent", the manufacturing method should be "Laundry Detergent manufacturing" for non-carbon tenants.

## Required Steps

1. **Access Tenant Type Information**
   - The tenant type (carbon/non-carbon) information should be available in the user's organization metadata
   - Add a GraphQL query to fetch tenant type information from the context
   - Query schema likely needs to expose `orgMetadata` that includes tenant type information

2. **Modify Manufacturing Method Prediction Logic**
   - Location: `web/src/components/AddProduct/AddProduct.tsx`
   - Current logic is in the `fetchManufacturingMethods` function
   - Changes needed:
     1. Add a function to get tenant type from context
     2. Modify the prediction logic to check tenant type first
     3. For non-carbon tenants:
        - Get the category name from `addProductForm.getFieldValue('category')`
        - Return `${categoryName} manufacturing` as the predicted method
     4. For carbon tenants:
        - Keep existing logic with `preferredManufacturingActivityName` mapping
        - Keep the default "Cleaning Products Manufacturing"

3. **Update Type Definitions**
   - If not already present, add tenant type information to relevant TypeScript interfaces
   - Update any GraphQL schema definitions if needed to expose tenant type

4. **Testing Requirements**
   - Test with both carbon and non-carbon tenants
   - Verify manufacturing method prediction works correctly for each case
   - Test cases should cover:
     - Non-carbon tenant with various product categories
     - Carbon tenant with matching preferred manufacturing name
     - Carbon tenant with default manufacturing name
     - Edge cases (empty/null category names)

## Example Implementation Pseudo-code

```typescript
// 1. Add function to check tenant type
const isNonCarbonTenant = (orgMetadata) => {
  return orgMetadata?.tenantType === 'non-carbon';
};

// 2. Modify manufacturing method prediction
const fetchManufacturingMethods = async () => {
  let predictedManufacturingMethod;
  const category = addProductForm.getFieldValue('category');

  // Check tenant type first
  if (isNonCarbonTenant(orgMetadata)) {
    predictedManufacturingMethod = `${category} manufacturing`;
  } else {
    // Existing carbon tenant logic
    predictedManufacturingMethod = preferredManufacturingActivityName[category]
      || preferredManufacturingActivityName.default;
  }

  // Rest of the existing code...
}
```

## Important Considerations

1. **Data Validation**
   - Handle cases where category name might be null/undefined
   - Sanitize category name before concatenating with "manufacturing"
   - Consider handling special characters or formatting in category names

2. **Backward Compatibility**
   - Ensure existing carbon tenant functionality remains unchanged
   - Consider data migration needs if any existing records need updating

3. **Error Handling**
   - Add appropriate error handling for tenant type checking
   - Provide fallback behavior if tenant type information is unavailable

4. **Performance**
   - Consider caching tenant type information to avoid repeated lookups
   - Optimize GraphQL queries to fetch tenant information efficiently

5. **UI/UX**
   - No direct UI changes needed, but verify the new manufacturing method names display correctly
   - Consider adding visual indicators if helpful for distinguishing tenant types

## Validation Criteria

1. For non-carbon tenants:
   - Manufacturing method should be "[category name] manufacturing"
   - This should work for any valid category name
   - Should handle edge cases gracefully

2. For carbon tenants:
   - Existing behavior should be unchanged
   - Should still use preferred mappings when available
   - Should fall back to "Cleaning Products Manufacturing" when no mapping exists

3. General:
   - No regression in existing functionality
   - Proper error handling
   - Clean code integration
   - Comprehensive test coverage
