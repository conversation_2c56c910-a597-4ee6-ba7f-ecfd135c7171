import { context } from '@redwoodjs/graphql-server'
import { UpdateOrgMetadata } from 'src/lib/auth'
import { GraphQLError } from 'graphql'

export const updateOrgMetadata = async ({ metadataKey, metadataValue }) => {
  try {
    const response = await UpdateOrgMetadata(
      context.currentUser.orgMemberInfo.orgId,
      metadataKey,
      metadataValue
    )

    return `Successfully updated ${metadataKey} to ${metadataValue}`
  } catch (error) {
    console.error(error)
    let errorMessage = 'Error updating organization metadata. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
