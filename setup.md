# CarbonBright Web Application Setup Guide

This guide provides step-by-step instructions for setting up and running the CarbonBright web application locally.

## Prerequisites

- **Node.js**: Version 18.x or 20.x (the application is built with RedwoodJS which requires Node.js >=18.x)
- **Yarn**: Version 1.15 or higher
- **Backend Services**:
  - LCA API service running on port 5005
  - ML Models service running on port 5001

## Setup Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd carbonbright-web
```

### 2. Configure Environment Variables

Create a `.env` file in the root directory with the following content:

```
LCA_API_ENDPOINT="http://127.0.0.1:5005"
PROPELAUTH_API_TOKEN="your-propelauth-api-token"
PROPELAUTH_AUTH_URL="your-propelauth-auth-url"
MAPBOX_SECRET_TOKEN="your-mapbox-secret-token"
TEST_USER_EMAIL="your-test-user-email"
TEST_USER_PASSWORD="your-test-user-password"
PROPELAUTH_LOGIN_URL="your-propelauth-login-url"
ML_MODELS_ENDPOINT="http://127.0.0.1:5001"
SENTRY_DSN=""
CONTACT_SUPPORT_EMAIL="your-contact-support-email"
CONTACT_SUPPORT_PASSWORD="your-contact-support-password"
CONTACT_SUPPORT_RECIPIENTS="your-contact-support-recipients"
FEEDBACK_EMAIL="your-feedback-email"
FEEDBACK_PASSWORD="your-feedback-password"
FEEDBACK_RECIPIENTS="your-feedback-recipients"
LABS_EF_MATCHING_QUOTA="20"
TEST_LAB_USER_EMAIL="your-test-lab-user-email"
TEST_LAB_USER_PASSWORD="your-test-lab-user-password"
TEST_TRIAL_USER_EMAIL="your-test-trial-user-email"
TEST_TRIAL_USER_PASSWORD="your-test-trial-user-password"
PROPELAUTH_VERIFIER_KEY="your-propelauth-verifier-key"
```

Replace the placeholder values with your actual credentials.

**Important Notes:**
- Do not use the "export" prefix in the `.env` file
- The `LCA_API_ENDPOINT` should point to your LCA API service (port 5005)
- The `ML_MODELS_ENDPOINT` should point to your ML Models service (port 5001)

### 3. Install Dependencies

Run the following command to install all required dependencies:

```bash
yarn install
```

This will install all the packages defined in the `package.json` file, including both the API and web dependencies.

### 4. Set Up the Database

Initialize the database with the following command:

```bash
yarn rw prisma migrate dev
```

When prompted for a migration name, enter a descriptive name such as "initial_setup".

This command will:
- Create a SQLite database file at `./dev.db`
- Apply the schema defined in `api/db/schema.prisma`
- Generate the Prisma client

### 5. Start the Development Server

Start the RedwoodJS development server:

```bash
yarn rw dev
```

This will start both the API and web servers:
- Web server: http://localhost:8910
- API server: http://localhost:8911

Your browser should automatically open to http://localhost:8910.

## Verifying Backend Services

To verify that your backend services are running correctly, you can use curl:

```bash
# Check LCA API service
curl -I http://127.0.0.1:5005

# Check ML Models service
curl -I http://127.0.0.1:5001
```

If the services are running, you should receive a response (even if it's a 404 Not Found, which just means you're hitting an endpoint that doesn't exist).

## Troubleshooting

### API Connection Issues

If you encounter issues connecting to the backend services:

1. Verify that the LCA API service is running on port 5005:
   ```bash
   curl -I http://127.0.0.1:5005
   ```

2. Verify that the ML Models service is running on port 5001:
   ```bash
   curl -I http://127.0.0.1:5001
   ```

3. Check that your `.env` file has the correct endpoint URLs:
   ```
   LCA_API_ENDPOINT="http://127.0.0.1:5005"
   ML_MODELS_ENDPOINT="http://127.0.0.1:5001"
   ```

### Database Issues

If you encounter database-related issues:

1. Reset the database:
   ```bash
   yarn rw prisma migrate reset
   ```

2. Check the database connection in `api/db/schema.prisma`:
   ```prisma
   datasource db {
     provider = "sqlite"
     url      = env("DATABASE_URL")
   }
   ```

3. Verify that `DATABASE_URL` is set correctly in `.env.defaults`:
   ```
   DATABASE_URL=file:./dev.db
   ```

## Additional Resources

- [RedwoodJS Documentation](https://redwoodjs.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [PropelAuth Documentation](https://docs.propelauth.com/)
